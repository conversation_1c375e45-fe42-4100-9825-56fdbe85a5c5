import { useEffect, useState } from "react"
import { checkLocalStorageHealth, clearLocalStorageWithConfirmation } from "@/lib/logger"

interface StorageHealth {
  totalSize: number
  itemCount: number
  largeItems: Array<{ key: string; size: number }>
  quotaExceeded: boolean
  isHealthy: boolean
  needsCleanup: boolean
}

/**
 * Hook to monitor localStorage health and provide cleanup utilities
 */
export const useStorageHealth = () => {
  const [health, setHealth] = useState<StorageHealth>({
    totalSize: 0,
    itemCount: 0,
    largeItems: [],
    quotaExceeded: false,
    isHealthy: true,
    needsCleanup: false,
  })

  const [isMonitoring, setIsMonitoring] = useState(false)

  const checkHealth = () => {
    try {
      const healthData = checkLocalStorageHealth()
      const isHealthy = !healthData.quotaExceeded && healthData.totalSize < 2000000 // < 2MB
      const needsCleanup = healthData.quotaExceeded || healthData.totalSize > 1500000 // > 1.5MB

      setHealth({
        ...healthData,
        isHealthy,
        needsCleanup,
      })

      return { ...healthData, isHealthy, needsCleanup }
    } catch (error) {
      console.error("Error checking storage health:", error)
      return health
    }
  }

  const startMonitoring = (intervalMs: number = 30000) => {
    if (isMonitoring) return

    setIsMonitoring(true)
    checkHealth() // Initial check

    const interval = setInterval(() => {
      const currentHealth = checkHealth()
      
      // Auto-cleanup if quota is exceeded and user hasn't been prompted recently
      if (currentHealth.quotaExceeded) {
        const lastPrompt = localStorage.getItem("last_cleanup_prompt")
        const now = Date.now()
        const oneHour = 60 * 60 * 1000

        if (!lastPrompt || now - parseInt(lastPrompt) > oneHour) {
          localStorage.setItem("last_cleanup_prompt", now.toString())
          
          // Show cleanup prompt
          setTimeout(() => {
            if (clearLocalStorageWithConfirmation()) {
              checkHealth() // Recheck after cleanup
            }
          }, 1000) // Delay to avoid blocking UI
        }
      }
    }, intervalMs)

    return () => {
      clearInterval(interval)
      setIsMonitoring(false)
    }
  }

  const stopMonitoring = () => {
    setIsMonitoring(false)
  }

  const forceCleanup = () => {
    return clearLocalStorageWithConfirmation()
  }

  const getHealthSummary = () => {
    const sizeInKB = Math.round(health.totalSize / 1024)
    const sizeInMB = Math.round(health.totalSize / (1024 * 1024))
    
    return {
      size: sizeInMB > 1 ? `${sizeInMB}MB` : `${sizeInKB}KB`,
      status: health.isHealthy ? "healthy" : health.needsCleanup ? "needs cleanup" : "warning",
      itemCount: health.itemCount,
      largestItem: health.largeItems[0] || null,
    }
  }

  // Auto-start monitoring on mount
  useEffect(() => {
    const cleanup = startMonitoring()
    return cleanup
  }, [])

  return {
    health,
    isMonitoring,
    checkHealth,
    startMonitoring,
    stopMonitoring,
    forceCleanup,
    getHealthSummary,
  }
}

/**
 * Simple hook to just check if localStorage is healthy
 */
export const useStorageHealthCheck = () => {
  const [isHealthy, setIsHealthy] = useState(true)
  const [needsCleanup, setNeedsCleanup] = useState(false)

  useEffect(() => {
    const check = () => {
      try {
        const health = checkLocalStorageHealth()
        setIsHealthy(!health.quotaExceeded && health.totalSize < 2000000)
        setNeedsCleanup(health.quotaExceeded || health.totalSize > 1500000)
      } catch (error) {
        console.error("Storage health check failed:", error)
        setIsHealthy(false)
        setNeedsCleanup(true)
      }
    }

    check()
    const interval = setInterval(check, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [])

  return { isHealthy, needsCleanup }
}
