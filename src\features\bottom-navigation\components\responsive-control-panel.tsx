import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"
import { useResponsiveLayout } from "@/middleware"
import { ControlPanel, type ControlPanelProps } from "../control-panel"

export interface ResponsiveControlPanelProps
  extends Omit<ControlPanelProps, "orientation"> {
  /**
   * Default orientation to use. If not provided, will use "horizontal" for desktop and "vertical" for mobile
   */
  defaultOrientation?: "horizontal" | "vertical"

  /**
   * Force a specific orientation regardless of screen size
   */
  forceOrientation?: "horizontal" | "vertical"

  /**
   * Whether to automatically switch orientation based on screen size
   * @default true
   */
  autoSwitch?: boolean

  /**
   * Breakpoint at which to switch from horizontal to vertical
   * @default "md"
   */
  switchBreakpoint?: "sm" | "md" | "lg" | "xl" | "2xl"

  /**
   * Whether to switch based on device orientation instead of screen size
   * @default false
   */
  useDeviceOrientation?: boolean

  /**
   * Additional class name for the wrapper
   */
  wrapperClassName?: string
}

/**
 * A responsive wrapper for the ControlPanel component that automatically
 * switches between horizontal and vertical orientation based on screen size
 * or device orientation.
 */
export function ResponsiveControlPanel({
  defaultOrientation,
  forceOrientation,
  autoSwitch = true,
  switchBreakpoint = "md",
  useDeviceOrientation = false,
  wrapperClassName,
  className,
  ...controlPanelProps
}: ResponsiveControlPanelProps) {
  // Get responsive layout information
  const { breakpoint, isLandscape, isMobile } = useResponsiveLayout()

  // Determine the initial orientation
  const getInitialOrientation = (): "horizontal" | "vertical" => {
    // If force orientation is set, use that
    if (forceOrientation) {
      return forceOrientation
    }

    // If default orientation is set, use that
    if (defaultOrientation) {
      return defaultOrientation
    }

    // Otherwise, determine based on device/screen
    if (useDeviceOrientation) {
      return isLandscape ? "horizontal" : "vertical"
    } else {
      // Use vertical for mobile, horizontal for larger screens
      return isMobile ? "vertical" : "horizontal"
    }
  }

  // State to track current orientation
  const [orientation, setOrientation] = useState<"horizontal" | "vertical">(
    getInitialOrientation()
  )

  // Update orientation when responsive conditions change
  useEffect(() => {
    if (!autoSwitch || forceOrientation) return

    let newOrientation: "horizontal" | "vertical"

    if (useDeviceOrientation) {
      // Base on device orientation
      newOrientation = isLandscape ? "horizontal" : "vertical"
    } else {
      // Base on breakpoint
      const breakpointValues = {
        sm: 1,
        md: 2,
        lg: 3,
        xl: 4,
        "2xl": 5,
      }

      const currentBreakpointValue = breakpointValues[breakpoint]
      const switchBreakpointValue = breakpointValues[switchBreakpoint]

      newOrientation =
        currentBreakpointValue < switchBreakpointValue
          ? "vertical"
          : "horizontal"
    }

    setOrientation(newOrientation)
  }, [
    autoSwitch,
    forceOrientation,
    useDeviceOrientation,
    isLandscape,
    breakpoint,
    switchBreakpoint,
  ])

  return (
    <div
      className={cn(
        "responsive-control-panel",
        orientation === "vertical" && "control-panel-vertical",
        wrapperClassName
      )}
    >
      <ControlPanel
        orientation={orientation}
        className={className}
        {...controlPanelProps}
      />
    </div>
  )
}
