# ImageMagick Installation Script for Windows
# This script installs ImageMagick CLI tools using Chocolatey package manager

param(
    [switch]$Force,
    [switch]$Quiet,
    [switch]$CheckOnly
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Color output functions
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-ErrorMsg { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

function Test-ImageMagickInstalled {
    try {
        $version = magick -version 2>$null
        if ($version -and $version -match "ImageMagick") {
            return $true
        }
    }
    catch {
        return $false
    }
    return $false
}

function Test-ChocolateyInstalled {
    try {
        $chocoVersion = choco --version 2>$null
        return $chocoVersion -ne $null
    }
    catch {
        return $false
    }
}

function Install-Chocolatey {
    Write-Info "Installing Chocolatey package manager..."
    
    # Check if running as administrator
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    
    if (-not $isAdmin) {
        Write-Error "Administrator privileges required to install Chocolatey."
        Write-Info "Please run this script as Administrator or install Chocolatey manually:"
        Write-Info "https://chocolatey.org/install"
        exit 1
    }

    try {
        # Set execution policy temporarily
        Set-ExecutionPolicy Bypass -Scope Process -Force
        
        # Download and install Chocolatey
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        # Refresh environment variables
        refreshenv
        
        Write-Success "Chocolatey installed successfully!"
        return $true
    }
    catch {
        Write-ErrorMsg "Failed to install Chocolatey: $($_.Exception.Message)"
        return $false
    }
}

function Install-ImageMagick {
    Write-Info "Installing ImageMagick via Chocolatey..."
    
    try {
        if ($Quiet) {
            choco install imagemagick -y --no-progress 2>$null
        }
        else {
            choco install imagemagick -y
        }
        
        # Refresh environment variables
        refreshenv
        
        Write-Success "ImageMagick installed successfully!"
        return $true
    }
    catch {
        Write-ErrorMsg "Failed to install ImageMagick: $($_.Exception.Message)"
        return $false
    }
}

function Show-ImageMagickInfo {
    try {
        Write-Info "ImageMagick Version Information:"
        magick -version
        Write-Info "`nImageMagick is ready for image optimization!"
    }
    catch {
        Write-Warning "Could not retrieve ImageMagick version information."
    }
}

# Main execution
Write-Info "🖼️  ImageMagick Installation Script"
Write-Info "=================================="

# Check if we're only checking installation status
if ($CheckOnly) {
    if (Test-ImageMagickInstalled) {
        Write-Success "ImageMagick is already installed and available."
        Show-ImageMagickInfo
        exit 0
    }
    else {
        Write-Warning "ImageMagick is not installed or not available in PATH."
        exit 1
    }
}

# Check if ImageMagick is already installed
if (Test-ImageMagickInstalled -and -not $Force) {
    Write-Success "ImageMagick is already installed!"
    Show-ImageMagickInfo
    exit 0
}

if ($Force -and (Test-ImageMagickInstalled)) {
    Write-Info "Force flag specified. Reinstalling ImageMagick..."
}

# Check if Chocolatey is installed
if (-not (Test-ChocolateyInstalled)) {
    Write-Warning "Chocolatey package manager not found."
    
    if (-not (Install-Chocolatey)) {
        Write-ErrorMsg "Failed to install Chocolatey. Cannot proceed with ImageMagick installation."
        exit 1
    }
}

# Install ImageMagick
$installResult = Install-ImageMagick

if ($installResult) {
    Write-Success "Installation completed successfully!"
    Show-ImageMagickInfo

    Write-Info "`n📋 Next Steps:"
    Write-Info "• Run 'pnpm images:analyze' to analyze your current images"
    Write-Info "• Run 'pnpm images:optimize' to start the optimization process"
    Write-Info "• Run 'pnpm images:help' for more options"
} else {
    Write-ErrorMsg "Installation failed. Please check the error messages above."
    exit 1
}
