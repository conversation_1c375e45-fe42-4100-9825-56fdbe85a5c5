# Bonus Bets Feature - Abstracted Architecture

This directory contains the refactored Bonus Bets Panel components with improved separation of concerns.

## Architecture Overview

The original monolithic `bonus-bets-panel.tsx` (478 lines) has been broken down into focused, reusable components:

### Components (`/components`)
- **`bonus-panel-inner.tsx`** - Core logic and state management
- **`dragon-layout.tsx`** - Dragon animation and layout component
- **`mobile-bonus-symbols.tsx`** - Mobile-specific symbol layout
- **`bonus-symbols-grid.tsx`** - Grid layout for bonus symbols

### Custom Hooks (`/hooks`)
- **`use-dragon-animation.ts`** - Dragon sprite sheet scroll animation
- **`use-dragon-preloader.ts`** - Image preloading logic
- **`use-symbol-sizing.ts`** - Responsive symbol sizing logic

### Constants (`/constants`)
- **`bonus-panel-styles.ts`** - Style constants and configuration

### Main Files
- **`bonus-bets-panel.tsx`** - Simplified entry point with error boundary (21 lines)
- **`bonus-symbol.tsx`** - Individual bonus symbol component (unchanged)
- **`index.ts`** - Comprehensive exports for all components and utilities

## Benefits

1. **Separation of Concerns** - Each component has a single responsibility
2. **Reusability** - Components and hooks can be used independently
3. **Maintainability** - Smaller, focused files are easier to understand and modify
4. **Testability** - Individual components can be tested in isolation
5. **Performance** - Better code splitting and tree shaking opportunities

## Backward Compatibility

All existing imports continue to work:
- `BonusBetsPanel` - Main component (unchanged API)
- `BonusSymbolsGrid` - Exported for backward compatibility
- All layout files continue to work without modification

## Usage

```typescript
// Main component (same as before)
import { BonusBetsPanel } from "@/features/bonus-bets"

// Individual components (new)
import { DragonLayout, MobileBonusSymbols } from "@/features/bonus-bets"

// Custom hooks (new)
import { useDragonAnimation, useSymbolSizing } from "@/features/bonus-bets"

// Constants (new)
import { BONUS_PANEL_STYLES, DRAGON_ASSETS } from "@/features/bonus-bets"
```

## File Size Reduction

- **Before**: 1 file, 478 lines
- **After**: 8 files, ~300 total lines (average 37 lines per file)
- **Main entry point**: 21 lines (95% reduction)

This refactoring follows the user's preference for self-documenting code with descriptive names, extracted constants, and well-named helper functions over JSDoc comments.
