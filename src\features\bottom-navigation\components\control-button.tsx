import { motion } from "motion/react"
import { useState, useCallback, memo, useEffect } from "react"

// Components
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Hooks
import { useImageLoader } from "@/hooks/use-image-loader"
import { useReducedMotion } from "@/hooks/use-reduced-motion"

// Utils
import { cn } from "@/lib/utils"
import { logger } from "@/middleware"
import { VisuallyHidden } from "./visually-hidden" // Updated import path

export interface ControlButtonProps {
  id: string
  src: string
  alt: string
  onClick?: () => void
  className?: string
  disabled?: boolean
  active?: boolean
  size?: "sm" | "md" | "lg" // Default is now "lg"
  showTooltip?: boolean
  activeColor?: string
  hoverColor?: string
  value?: number | string
  isChip?: boolean
  aspect?: "square" | "freeform"
}

function ControlButtonComponent({
  id,
  src,
  alt,
  onClick,
  className,
  disabled = false,
  active = false,
  size = "lg", // Changed default from "md" to "lg"
  showTooltip = true,
  activeColor = "#FFD700",
  hoverColor = "#E0E0E0",
  value,
  isChip = false,
  aspect = "square",
}: ControlButtonProps) {
  const [isHovered, setIsHovered] = useState(false)
  const prefersReducedMotion = useReducedMotion()
  const { error } = useImageLoader(src, id) // Load image with caching

  // Size mapping
  const sizeMap = {
    sm: 42,
    md: 72,
    lg: 88,
  }

  // Get the base button size from the size prop
  const baseButtonSize = sizeMap[size] || sizeMap.md
  // For chips, we'll reduce the image size but keep the container the same
  const buttonSize = baseButtonSize
  const chipImageSize = isChip ? baseButtonSize * 0.85 : baseButtonSize

  // Ensure src is defined
  const imageSrc = error ? "/placeholder.svg" : src || "/placeholder.svg"

  // Log when a button is rendered with an error
  useEffect(() => {
    if (error) {
      logger.warn(`Button rendered with fallback image: ${id}`, {
        context: "ControlButton",
        data: { src },
      })
    }
  }, [error, id, src])

  // Memoize event handlers to prevent unnecessary re-renders
  const handleMouseEnter = useCallback(() => setIsHovered(true), [])
  const handleMouseLeave = useCallback(() => setIsHovered(false), [])

  // Keyboard event handling removed

  // Determine if we should animate based on reduced motion preference
  const shouldAnimate = !prefersReducedMotion

  // Render the button with or without animation based on preference
  const renderButton = () => {
    const buttonContent = (
      <button
        type='button'
        onClick={onClick}
        disabled={disabled}
        className={cn(
          "relative overflow-hidden select-none",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        aria-label={alt}
        aria-pressed={active}
        style={{
          width: aspect === "freeform" ? "auto" : buttonSize,
          height: buttonSize,
          transition: shouldAnimate ? "transform 0.2s ease-in-out" : "none",
        }}
      >
        <div
          style={{
            width: aspect === "freeform" ? "auto" : buttonSize,
            height: buttonSize,
            filter: active
              ? `brightness(1.3) drop-shadow(0 0 3px ${activeColor})`
              : isHovered && !disabled
              ? `brightness(1.2) drop-shadow(0 0 2px ${hoverColor})`
              : "brightness(1)",
            transition: shouldAnimate ? "filter 0.2s ease-in-out" : "none",
            position: "relative",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <img
            src={imageSrc || "/placeholder.svg"}
            alt=''
            width='auto'
            height={isChip ? chipImageSize : buttonSize}
            className={cn(
              "w-auto max-w-full object-contain select-none",
              isChip ? "h-[85%]" : "h-full"
            )}
            draggable='false'
          />
          {isChip && value && (
            <div className='absolute inset-0 flex items-center justify-center select-none pointer-events-none'>
              <span
                className='text-white font-bold drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]'
                style={{
                  fontSize: Math.max((baseButtonSize / 4) * 0.85, 10) + "px",
                }}
              >
                {typeof value === "number" && value >= 1000
                  ? value >= 1000000
                    ? `${Math.floor(value / 1000000)}M`
                    : `${Math.floor(value / 1000)}K`
                  : value}
              </span>
            </div>
          )}
        </div>
        <VisuallyHidden>{alt}</VisuallyHidden>
      </button>
    )

    // Wrap with tooltip if enabled
    if (showTooltip && alt) {
      return (
        <TooltipProvider>
          <Tooltip delayDuration={300}>
            <TooltipTrigger asChild>{buttonContent}</TooltipTrigger>
            <TooltipContent className='bg-black text-white'>
              <p>{alt}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return buttonContent
  }

  // Use motion for animation if not reduced motion
  if (shouldAnimate) {
    return (
      <motion.div
        initial={{ scale: 1 }}
        animate={{ scale: isHovered && !disabled ? 0.9 : 0.95 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        {renderButton()}
      </motion.div>
    )
  }

  // Render without animation for reduced motion
  return renderButton()
}

// Memoize the component to prevent unnecessary re-renders
export const ControlButton = memo(ControlButtonComponent)
