import { useEffect } from "react"
import { useRoundPhase } from "@/hooks"
import { useBettingStore } from "@/stores/betting-store"
import { useGameDataManagement } from "./use-game-data-management"

interface WinningsCalculationDependencies {
  currentBetslipId: number | null
  isSpinningPhase: boolean
  isResultingPhase: boolean
}

export const useWinningsCalculation = (): void => {
  const { Spinning: isSpinningPhase, Resulting: isResultingPhase } = useRoundPhase()
  const currentBetslipId = useBettingStore((state) => state.betslipId)
  const { calculateCurrentWinnings } = useGameDataManagement()

  const winningsCalculationDependencies: WinningsCalculationDependencies = {
    currentBetslipId,
    isSpinningPhase,
    isResultingPhase
  }

  const shouldRecalculateWinnings = (): boolean => {
    return Boolean(
      winningsCalculationDependencies.currentBetslipId &&
      (winningsCalculationDependencies.isSpinningPhase || winningsCalculationDependencies.isResultingPhase)
    )
  }

  useEffect(() => {
    if (shouldRecalculateWinnings()) {
      calculateCurrentWinnings()
    }
  }, [
    winningsCalculationDependencies.currentBetslipId,
    winningsCalculationDependencies.isSpinningPhase,
    winningsCalculationDependencies.isResultingPhase,
    calculateCurrentWinnings
  ])
}
