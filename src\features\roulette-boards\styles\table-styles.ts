// Define style constants for reuse
export const STYLES = {
  // Base cell styling
  cellBase: "relative flex items-center justify-center rounded-lg",
  cellText: "text-white font-bold",

  // Text sizes
  textSizes: {
    standard: "text-2xl md:text-3xl lg:text-4xl",
    large: "text-2xl md:text-4xl lg:text-5xl",
    medium: "text-xl md:text-3xl lg:text-4xl",
  },

  // Cell colors
  colors: {
    green: "bg-[#0cae12] text-white",
    red: "bg-[#be1616] text-white",
    black: "bg-[#252525] text-white",
    greyGradient:
      "bg-gradient-to-b from-[#617c59] via-[#496343b0] to-[#2a442580] text-white",
  },

  specialsButtons: {
    textContainer: "leading-none",
    buttonBase:
      "bg-black/60 font-bold uppercase text-lg p-2 rounded-md w-full h-full max-h-[100px] max-w-[150px] border-2",
    smallSeries: " border-red-500 text-red-500",
    orphans: " border-blue-500 text-blue-500",
    zeroSpiel: " border-green-500 text-green-500",
    bigSeries: " border-yellow-500 text-yellow-500",
  },

  // Layout
  mainContainer: "w-full h-full",
  tableGrid:
    "w-full h-full bg-black/30 rounded-lg relative p-0.5 grid grid-cols-[1.25fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr_1fr_1.25fr] grid-rows-[5fr_6fr_6fr_6fr_5fr] gap-0.5",
}

// Function to get the color class for a number
export const getNumberColorClass = (
  num: string,
  redNumbers: number[]
): string => {
  const numInt = parseInt(num)
  if (num === "0") return STYLES.colors.green // Bright green for zero
  return redNumbers.includes(numInt)
    ? STYLES.colors.red // Bright red
    : STYLES.colors.black // True black
}
