# PowerShell script to add missing imports to Online.tsx
Write-Host "Adding missing imports to Online.tsx..." -ForegroundColor Green

$filePath = "src/layouts/Online.tsx"
$content = Get-Content $filePath -Raw

# Define the imports to add
$importsToAdd = @(
    'import { MobileTopSection, MobileBettingArea } from "@/components/online/mobile-layout-sections";',
    'import { DesktopTopInformationRow, DesktopBettingArea } from "@/components/online/desktop-layout-sections";',
    'import ControlPanelsSection from "@/components/online/control-panels-section";',
    'import { LAYOUT_CONSTANTS } from "@/components/online/layout-constants";'
)

# Find the line with ResultOverlay import
$resultOverlayLine = 'import { ResultOverlay } from "@/features/resulting/result-overlay";'

# Check if imports already exist
$missingImports = @()
foreach ($import in $importsToAdd) {
    if ($content -notmatch [regex]::Escape($import)) {
        $missingImports += $import
    }
}

if ($missingImports.Count -eq 0) {
    Write-Host "All imports already exist!" -ForegroundColor Green
    exit 0
}

Write-Host "Adding $($missingImports.Count) missing imports..." -ForegroundColor Yellow

# Add the missing imports after the ResultOverlay import
$newContent = $content -replace [regex]::Escape($resultOverlayLine), ($resultOverlayLine + "`n" + ($missingImports -join "`n"))

# Write the updated content back to the file
Set-Content -Path $filePath -Value $newContent -NoNewline

Write-Host "Successfully added imports to Online.tsx!" -ForegroundColor Green

# Verify the imports were added
$updatedContent = Get-Content $filePath -Raw
foreach ($import in $importsToAdd) {
    if ($updatedContent -match [regex]::Escape($import)) {
        Write-Host "✓ $import" -ForegroundColor Green
    } else {
        Write-Host "✗ $import" -ForegroundColor Red
    }
}
