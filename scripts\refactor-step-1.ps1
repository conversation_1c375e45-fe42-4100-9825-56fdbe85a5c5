# PowerShell script for incremental layout refactoring - Step 1
# This script helps with the first phase of layout consolidation

Write-Host "=== Layout Consolidation - Step 1 ===" -ForegroundColor Green
Write-Host "Adding missing imports to Online.tsx" -ForegroundColor Yellow

# Check if the required component files exist
$requiredFiles = @(
    "src/components/online/mobile-layout-sections.tsx",
    "src/components/online/desktop-layout-sections.tsx",
    "src/components/online/control-panels-section.tsx",
    "src/components/online/layout-constants.ts"
)

Write-Host "Checking required component files..." -ForegroundColor Cyan
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    }
    else {
        Write-Host "✗ $file missing" -ForegroundColor Red
        exit 1
    }
}

# Check current imports in Online.tsx
Write-Host "`nChecking current imports in Online.tsx..." -ForegroundColor Cyan
$onlineContent = Get-Content "src/layouts/Online.tsx" -Raw

$missingImports = @()
if ($onlineContent -notmatch "MobileTopSection") {
    $missingImports += "MobileTopSection, MobileBettingArea from mobile-layout-sections"
}
if ($onlineContent -notmatch "DesktopTopInformationRow") {
    $missingImports += "DesktopTopInformationRow, DesktopBettingArea from desktop-layout-sections"
}
if ($onlineContent -notmatch "ControlPanelsSection") {
    $missingImports += "ControlPanelsSection from control-panels-section"
}
if ($onlineContent -notmatch "LAYOUT_CONSTANTS") {
    $missingImports += "LAYOUT_CONSTANTS from layout-constants"
}

if ($missingImports.Count -gt 0) {
    Write-Host "Missing imports detected:" -ForegroundColor Yellow
    foreach ($import in $missingImports) {
        Write-Host "  - $import" -ForegroundColor Red
    }
    Write-Host "`nPlease add these imports manually to Online.tsx" -ForegroundColor Yellow
}
else {
    Write-Host "All required imports are present!" -ForegroundColor Green
}

# Check for old constants that should be removed
Write-Host "`nChecking for old constants to remove..." -ForegroundColor Cyan
$oldConstants = @("MOBILE_LAYOUT_CONSTANTS", "ANIMATION_CONSTANTS", "GAME_SECTION_STYLES")
$constantsToRemove = @()

foreach ($constant in $oldConstants) {
    if ($onlineContent -match "const $constant = ") {
        $constantsToRemove += $constant
    }
}

if ($constantsToRemove.Count -gt 0) {
    Write-Host "Old constants found that should be removed:" -ForegroundColor Yellow
    foreach ($constant in $constantsToRemove) {
        Write-Host "  - $constant" -ForegroundColor Red
    }
}
else {
    Write-Host "No old constants found - good!" -ForegroundColor Green
}

Write-Host "`n=== Next Steps ===" -ForegroundColor Green
Write-Host "1. Add missing imports to Online.tsx" -ForegroundColor White
Write-Host "2. Remove old constant definitions" -ForegroundColor White
Write-Host "3. Run step 2 script to replace component usage" -ForegroundColor White

Write-Host "`nStep 1 analysis complete!" -ForegroundColor Green
