// Layout configuration constants for self-documenting grid layouts
export const LAYOUT_CONSTANTS = {
  HISTORY_DISPLAY_LIMIT: 15,
  HISTORY_FETCH_LIMIT: 16,
  CONTROL_PANEL_GRID_COLUMNS: 24,
  
  MOBILE_VIEWPORT: {
    MAX_HEIGHT: "100vh",
    TOP_SECTION_HEIGHT: "40vh",
    HISTORY_HEIGHT: "5vh", 
    VIDEO_HEIGHT: "24vh",
    BETTING_AREA_HEIGHT: "70vh",
    CONTROLS_HEIGHT: "20vh"
  },
  
  DESKTOP_GRID: {
    COLUMNS: "0.9fr 1fr 1.175fr 1fr",
    ROWS: "minmax(0,1fr) minmax(0,2fr) auto"
  },
  
  MOBILE_GRID: {
    COLUMNS: "auto 1fr",
    ROWS: "auto 1fr auto"
  },
  
  CONTROL_PANEL_SPANS: {
    BET_OPTIONS: 9,
    CHIPS: 6,
    CONTROLS: 9
  },
  
  Z_INDEX: {
    OVERLAYS: 30,
    SIDE_MENU: 30,
    BETTING_CONTROLS: 30
  },
  
  ANIMATION: {
    DURATION: 500,
    EASING: "ease-in-out",
    SPRING: {
      STIFFNESS: 100,
      DAMPING: 15
    }
  }
} as const

// Styling constants for consistent theming
export const GAME_SECTION_STYLES = {
  STANDARD_SHADOW: "1px 1px 0px 0.2px rgba(255, 255, 255, 0.5)",
  VIDEO_SHADOW: "1px 1px 0px 1px rgba(12, 174, 18, 0.75)",
  BONUS_BORDER: "border-2 border-[#c69e61]",
  BACKGROUND_OVERLAY: "bg-black/40"
} as const

// Game phase styling configurations
export const PHASE_DEPENDENT_STYLES = {
  BETTING_ACTIVE: {
    MOBILE_TRANSFORM: "-mt-[20%] transform translate-y-0",
    GRID_LAYOUT: "grid-cols-[auto_1fr]",
    SHADOW: "shadow-lg"
  },
  BETTING_INACTIVE: {
    MOBILE_TRANSFORM: "mt-0 transform", 
    GRID_LAYOUT: "grid-cols-[1fr_1fr]",
    SHADOW: ""
  }
} as const

// Type definitions for better self-documentation
export interface LayoutBreakpoints {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
}

export interface GamePhaseStates {
  isBettingPhase: boolean
  isSpinningPhase: boolean
  isResultingPhase: boolean
}

export interface PlayerFinancialData {
  currentBalance: number
  totalBetAmount: number
  lastWinAmount: number
}
