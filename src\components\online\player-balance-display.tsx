import Divider from "@/components/ui/Divider"
import { formatCurrency } from "@/lib/formatting"
import { PlayerFinancialData } from "./layout-constants"

interface PlayerBalanceDisplayProps extends PlayerFinancialData {
  displayMode: "mobile" | "desktop"
}

interface BalanceItemProps {
  label: string
  amount: number
  className?: string
}

const BalanceItem = ({ label, amount, className = "" }: BalanceItemProps) => (
  <span className={className}>
    {label}: {formatCurrency(amount)}
  </span>
)

const MobileBalanceLayout = ({ 
  currentBalance, 
  totalBetAmount, 
  lastWinAmount 
}: PlayerFinancialData) => (
  <div className="w-full flex justify-between items-center whitespace-nowrap text-xs p-1">
    <BalanceItem 
      label="Balance" 
      amount={currentBalance} 
      className="max-w-12" 
    />
    <BalanceItem 
      label="Total bet" 
      amount={totalBetAmount} 
      className="max-w-12" 
    />
    <BalanceItem 
      label="Last Win" 
      amount={lastWinAmount} 
      className="max-w-12" 
    />
  </div>
)

const DesktopBalanceLayout = ({ 
  currentBalance, 
  totalBetAmount 
}: Pick<PlayerFinancialData, "currentBalance" | "totalBetAmount">) => (
  <div className="w-full flex justify-center items-center whitespace-nowrap py-1">
    <BalanceItem label="Balance" amount={currentBalance} />
    <Divider className="rotate-90 w-10" />
    <BalanceItem label="Total bet" amount={totalBetAmount} />
  </div>
)

const PlayerBalanceDisplay = ({ 
  displayMode, 
  currentBalance, 
  totalBetAmount, 
  lastWinAmount 
}: PlayerBalanceDisplayProps) => {
  const isMobileDisplay = displayMode === "mobile"
  
  if (isMobileDisplay) {
    return (
      <MobileBalanceLayout
        currentBalance={currentBalance}
        totalBetAmount={totalBetAmount}
        lastWinAmount={lastWinAmount}
      />
    )
  }
  
  return (
    <DesktopBalanceLayout
      currentBalance={currentBalance}
      totalBetAmount={totalBetAmount}
    />
  )
}

export default PlayerBalanceDisplay
