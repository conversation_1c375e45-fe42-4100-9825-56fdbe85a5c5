import js from "@eslint/js";
import importPlugin from "eslint-plugin-import";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import globals from "globals";
import tseslint from "typescript-eslint";

export default tseslint.config(
  { ignores: ["dist"] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ["**/*.{ts,tsx}"],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      import: importPlugin,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      "react-hooks/exhaustive-deps": "off",
      "typescript-eslint/no-explicit-any": "off",
      "typescript-eslint/no-implicit-any": "off",
      "react-hooks/react-compiler": "warn",
      "react-refresh/only-export-components": [
        "warn",
        { allowConstantExport: true },
      ],
      // Path alias rules
      "import/no-relative-parent-imports": "warn",
      "import/order": [
        "warn",
        {
          groups: [
            "builtin",
            "external",
            "internal",
            "parent",
            "sibling",
            "index",
          ],
          pathGroups: [
            { pattern: "@/**", group: "internal" },
            { pattern: "@components/**", group: "internal" },
            { pattern: "@features/**", group: "internal" },
            { pattern: "@hooks/**", group: "internal" },
            { pattern: "@lib/**", group: "internal" },
            { pattern: "@middleware/**", group: "internal" },
            { pattern: "@stores/**", group: "internal" },
            { pattern: "@views/**", group: "internal" },
            { pattern: "@config/**", group: "internal" },
            { pattern: "@assets/**", group: "internal" },
          ],
          pathGroupsExcludedImportTypes: ["builtin"],
          alphabetize: { order: "asc", caseInsensitive: true },
        },
      ],
    },
  },
);
