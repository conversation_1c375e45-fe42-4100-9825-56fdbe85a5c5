import { useEffect, useRef, useState } from "react";

// Hooks
import { getHistory, getWinnings, History, useRoundPhase } from "@/hooks";
import { useAudioController } from "@/hooks/game/use-audio-controller";
import { useGameNotifications } from "@/hooks/game/use-game-notifications";
import { useMobile } from "@/hooks/use-mobile";

// Stores
import { useInitialization } from "@/lib/initialization";
import { cn } from "@/lib/utils";
import { useBettingStore } from "@/stores/betting-store";
import { useGameStateStore } from "@/stores/game-state-store";

// Layout Components
import { ResultOverlay } from "@/features/resulting/result-overlay";
import { bonusSelectors } from "@/config";
import BettingOverlay from "@/components/common/betting-overlay";
import VideoPlayer from "@/features/streaming/video-player";

const HISTORY_DISPLAY_LIMIT = 15;
const HISTORY_FETCH_LIMIT = 16;
const EMPTY_BETSLIP_ID = 0;
const NO_WINNINGS = 0;

const MOBILE_LAYOUT_CONSTANTS = {
  TOP_SECTION_HEIGHT: "40vh",
  HISTORY_HEIGHT: "5vh",
  VIDEO_HEIGHT: "24vh",
  BETTING_AREA_HEIGHT: "70vh",
  CONTROLS_HEIGHT: "20vh",
  BETTING_TRANSFORM_ACTIVE: "-mt-[20%] transform translate-y-0",
  BETTING_TRANSFORM_INACTIVE: "mt-0 transform",
  GRID_COLS_BETTING: "grid-cols-[auto_1fr]",
  GRID_COLS_NORMAL: "grid-cols-[1fr_1fr]",
};

const ANIMATION_CONSTANTS = {
  DURATION: 0.5,
  SPRING_STIFFNESS: 100,
  SPRING_DAMPING: 15,
  TRANSITION_TIMING: "duration-500 ease-in-out",
};

const GAME_SECTION_STYLES = {
  BONUS_BORDER: "border-2 border-[#c69e61]",
  BACKGROUND_OVERLAY: "bg-black/40",
  SHADOW_ACTIVE: "shadow-lg",
};

const SPECIAL_GAME_TYPE = "special";

function Online() {
  useAudioController();
  useInitialization();
  useGameNotifications();

  const {
    Resulting: isResultingPhase,
    Betting: isBettingPhase,
    Spinning: isSpinningPhase,
  } = useRoundPhase();
  const currentRoundData = useGameStateStore((state) => state.roundData);
  const currentGameType = useGameStateStore((state) => state.gameType);
  const isMobileDevice = useMobile();
  const { setWinnings, winnings: currentWinnings } = useBettingStore();
  const currentBetslipId = useBettingStore((state) => state.betslipId);
  const playerBalance = useBettingStore((state) => state.balance);
  const totalBetAmount = useBettingStore((state) => state.totalBet);

  useEffect(() => {
    const calculatePlayerWinnings = async () => {
      if (currentBetslipId && currentBetslipId !== EMPTY_BETSLIP_ID) {
        const calculatedWinnings = await getWinnings(currentBetslipId);
        setWinnings(calculatedWinnings);
      } else {
        setWinnings(NO_WINNINGS);
      }
    };

    calculatePlayerWinnings();
  }, [currentBetslipId, isSpinningPhase, isResultingPhase]);

  const [gameHistoryResults, setGameHistoryResults] = useState<History[]>([]);
  const lastProcessedRoundIdRef = useRef<string | null>(null);

  const { betOptionsButtons, chipsButtons, controlsButtons } =
    useControlPanels();
  const standardControlPanelConfiguration = {
    showTooltips: true,
    theme: "default" as const,
    autoSwitch: true,
    useDeviceOrientation: true,
  };

  useEffect(() => {
    if (
      !isResultingPhase ||
      !currentRoundData ||
      !currentRoundData.rouletteNumber ||
      !currentRoundData.bonusNumber
    ) {
      return;
    }

    const currentRoundId = currentRoundData.rouletteDrawId?.toString();

    if (!currentRoundId || currentRoundId === lastProcessedRoundIdRef.current) {
      return;
    }

    lastProcessedRoundIdRef.current = currentRoundId;
  }, [isResultingPhase, currentRoundData]);

  useEffect(() => {
    if (!currentRoundData) return;

    const refreshGameHistory = async () => {
      const fetchedHistory = await getHistory(HISTORY_FETCH_LIMIT);
      setGameHistoryResults(fetchedHistory.reverse());
    };

    refreshGameHistory();
  }, [currentRoundData]);

  const GameHistoryDisplay = () => {
    const displayedHistoryResults = gameHistoryResults.slice(
      0,
      HISTORY_DISPLAY_LIMIT,
    );

    return (
      <section className="flex snap-x snap-mandatory gap-3 overflow-x-auto bg-black">
        {displayedHistoryResults.map(
          (historyEntry: History | number, entryIndex: number) => (
            <span
              key={entryIndex}
              className="flex snap-center flex-col items-center justify-center text-center align-middle text-xxs font-bold"
            >
              {typeof historyEntry === "number" ? (
                historyEntry
              ) : (
                <>
                  {historyEntry.rouletteNumber}
                  <img
                    className="aspect-square w-4"
                    src={
                      bonusSelectors[historyEntry.bonusNumber - 1]?.bonusSource
                    }
                    alt="bonus symbol"
                  />
                </>
              )}
            </span>
          ),
        )}
      </section>
    );
  };

  return (
    <main
      className={cn(
        "h-[100vh] max-h-[100vh] grid gap-4",
        "grid-cols-[auto_1fr] grid-rows-[auto_1fr_auto]",
        "md:grid-cols-[0.9fr_1fr_1.175fr_1fr] md:grid-rows-[minmax(0,1fr)_minmax(0,2fr)_auto]",
        "overflow-hidden",
        "md:px-5 md:pt-5",
        "relative",
      )}
    >
      <ResultOverlay />

      {isMobileDevice && (
        <>
          <BettingOverlay />
          <div
            className={`col-span-full w-full row-start-1 relative max-h-[${MOBILE_LAYOUT_CONSTANTS.TOP_SECTION_HEIGHT}] overflow-hidden`}
          >
            <div
              className={`h-auto max-h-[${MOBILE_LAYOUT_CONSTANTS.HISTORY_HEIGHT}] z-30`}
            >
              <GameHistoryDisplay />
            </div>
            <div
              className={`overflow-hidden transition-all ${ANIMATION_CONSTANTS.TRANSITION_TIMING} max-h-[${MOBILE_LAYOUT_CONSTANTS.VIDEO_HEIGHT}]`}
            >
              <VideoPlayer />
            </div>
            <span className="absolute top-8 left-0 z-30 max-w-8">
              <SideMenu />
            </span>
          </div>
        </>
      )}

      {!isMobileDevice && (
        <>
          {/* History section */}
          <GameSection
            hasInsetShadow={false}
            className="w-full h-full col-start-1 row-start-1"
            title="History"
          >
            <section className="flex h-full flex-col gap-2">
              <SideMenu />
              <WinnersPanel />
            </section>
          </GameSection>

          {/* Statistics panel */}
          <GameSection
            style={{
              boxShadow: "1px 1px 0px 0.2px rgba(255, 255, 255, 0.5)",
            }}
            className="col-start-2 row-start-1 bg-black/40"
            title="Statistics"
          >
            <StatisticsPanel />
          </GameSection>

          {/* Video player */}
          <GameSection
            style={{
              boxShadow: "1px 1px 0px 1px rgba(12, 174, 18, 0.75)",
            }}
            className="w-full h-full overflow-hidden col-start-3 row-start-1 relative"
            title="Video"
          >
            <VideoPlayer />
          </GameSection>

          {/* Past results */}
          <GameSection
            style={{
              boxShadow: "1px 1px 0px 0.2px rgba(255, 255, 255, 0.5)",
            }}
            className="col-start-4 row-start-1 bg-black/40"
            title="Past Results"
          >
            <PastResultsPanel />
          </GameSection>
        </>
      )}

      {isMobileDevice ? (
        <div
          className={cn(
            "col-span-full row-start-2 px-4 grid gap-3 relative z-30",
            `max-h-[${MOBILE_LAYOUT_CONSTANTS.BETTING_AREA_HEIGHT}] min-h-0 overflow-hidden`,
            `transition-all ${ANIMATION_CONSTANTS.TRANSITION_TIMING}`,
            isBettingPhase
              ? MOBILE_LAYOUT_CONSTANTS.BETTING_TRANSFORM_ACTIVE
              : MOBILE_LAYOUT_CONSTANTS.BETTING_TRANSFORM_INACTIVE,
            isBettingPhase
              ? MOBILE_LAYOUT_CONSTANTS.GRID_COLS_BETTING
              : MOBILE_LAYOUT_CONSTANTS.GRID_COLS_NORMAL,
          )}
        >
          <section className="flex flex-col gap-1">
            <GameSection
              className={cn(
                GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
                GAME_SECTION_STYLES.BONUS_BORDER,
                "w-auto min-h-0 rounded-[4px] flex flex-col overflow-hidden",
                `transition-all ${ANIMATION_CONSTANTS.TRANSITION_TIMING}`,
                isBettingPhase ? GAME_SECTION_STYLES.SHADOW_ACTIVE : "",
                isBettingPhase ? "border-none" : "",
              )}
              title="Bonus Bets"
            >
              <div className="flex-1 min-h-0 overflow-hidden">
                <BonusBetsPanel />
              </div>
            </GameSection>
            {!isBettingPhase && (
              <GameSection
                className={cn(
                  GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
                  GAME_SECTION_STYLES.BONUS_BORDER,
                  "p-0 h-auto rounded-[4px] w-auto min-h-0 flex flex-col overflow-auto",
                  `transition-all ${ANIMATION_CONSTANTS.TRANSITION_TIMING}`,
                  isBettingPhase ? GAME_SECTION_STYLES.SHADOW_ACTIVE : "",
                )}
                title="Bonus Bets"
              >
                <motion.div
                  key="bonus-symbols-grid"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{
                    duration: ANIMATION_CONSTANTS.DURATION,
                    type: "spring",
                    stiffness: ANIMATION_CONSTANTS.SPRING_STIFFNESS,
                    damping: ANIMATION_CONSTANTS.SPRING_DAMPING,
                  }}
                  className="w-full flex items-end justify-center"
                >
                  <BonusSymbolsGrid isMobile={isMobileDevice} />
                </motion.div>
              </GameSection>
            )}
          </section>

          <GameSection
            hasInsetShadow={false}
            className={cn(
              "overflow-hidden min-h-0 flex flex-col",
              `transition-all ${ANIMATION_CONSTANTS.TRANSITION_TIMING}`,
              currentGameType === SPECIAL_GAME_TYPE
                ? ""
                : GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
            )}
            title="Betting Table"
          >
            <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
              <RouletteTable />
              <NeighboursButtons />
            </div>
          </GameSection>
        </div>
      ) : (
        <>
          <GameSection
            className={cn(
              GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
              GAME_SECTION_STYLES.BONUS_BORDER,
              "col-start-1 row-start-2 flex-grow min-h-0 flex flex-col",
            )}
            title="Bonus Bets"
          >
            <div className="flex-1 min-h-0 overflow-hidden">
              <BonusBetsPanel />
            </div>
          </GameSection>

          <GameSection
            hasInsetShadow={false}
            className={cn(
              "col-start-2 row-start-2 col-span-full flex-grow overflow-hidden min-h-0 flex flex-col",
              currentGameType === SPECIAL_GAME_TYPE
                ? "col-span-3"
                : GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
            )}
            title="Betting Table"
          >
            <div className="flex flex-1 gap-1 flex-row min-h-0 overflow-hidden">
              <RouletteTable />
              <NeighboursButtons />
            </div>
          </GameSection>
        </>
      )}

      <section
        className={`col-span-full row-start-3 min-h-0 mx-4 md:mx-0 max-h-[${MOBILE_LAYOUT_CONSTANTS.CONTROLS_HEIGHT}] z-30`}
      >
        <div
          className={cn(
            "w-full grid grid-cols-[repeat(24,1fr)] grid-rows-1 gap-4 min-h-0",
          )}
        >
          <GameSection
            hasInsetShadow={false}
            className="w-full h-auto md:row-span-1 col-span-9"
            title="Bet Options"
          >
            <ControlPanel
              buttons={betOptionsButtons}
              {...standardControlPanelConfiguration}
            />
          </GameSection>
          <FavoritesPopover />

          <GameSection
            hasInsetShadow={false}
            className="w-full h-auto md:row-span-1 col-span-6"
            title="Chips"
          >
            <ControlPanel
              buttons={chipsButtons}
              zeroGapForChips={true}
              {...standardControlPanelConfiguration}
            />
          </GameSection>

          <GameSection
            hasInsetShadow={false}
            className="w-full h-auto md:row-span-1 col-span-9"
            title="Controls"
          >
            <ControlPanel
              buttons={controlsButtons}
              {...standardControlPanelConfiguration}
            />
          </GameSection>
        </div>

        {isMobileDevice ? (
          <div className="w-full flex justify-between items-center whitespace-nowrap text-xs p-1">
            <span className="max-w-12">
              Balance:
              <br /> {formatCurrency(playerBalance || 0)}
            </span>
            <span className="max-w-12">
              Total bet:
              <br /> {formatCurrency(totalBetAmount || 0)}
            </span>
            <span className="max-w-12">
              Last Win:
              <br /> {formatCurrency(currentWinnings || 0)}
            </span>
          </div>
        ) : (
          <div className="w-full flex justify-center items-center whitespace-nowrap py-1">
            <span>Balance: {formatCurrency(playerBalance || 0)}</span>
            <Divider className="rotate-90 w-10" />
            <span>Total bet: {formatCurrency(totalBetAmount || 0)}</span>
          </div>
        )}
      </section>
    </main>
  );
}

export default Online;
