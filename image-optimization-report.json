{"summary": {"startTime": "2025-05-29T15:01:16.455Z", "endTime": "2025-05-29T15:01:32.668Z", "totalImages": 30, "analyzedImages": 30, "optimizedImages": 10, "totalSavings": 11476245, "errors": 0}, "optimized": [{"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\neck.png", "relativePath": "public\\assets\\images\\dragon\\neck.png", "size": 2306249, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": true, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.367Z", "result": {"OriginalSize": 2306249, "QualityAdjusted": true, "OptimizedSize": 119660, "FinalQuality": 70, "SavingsPercent": 94.81148826514396, "Success": true, "Savings": 2186589}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\neck.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\head.png", "relativePath": "public\\assets\\images\\dragon\\head.png", "size": 926493, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": true, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.347Z", "result": {"SavingsPercent": 86.06918778663196, "OriginalSize": 926493, "OptimizedSize": 129068, "Success": true, "Savings": 797425}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\head.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\hand-right.png", "relativePath": "public\\assets\\images\\dragon\\hand-right.png", "size": 152419, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": false, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.400Z", "result": {"SavingsPercent": 87.74431009257377, "OriginalSize": 152419, "OptimizedSize": 18680, "Success": true, "Savings": 133739}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\hand-right.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\hand-left.png", "relativePath": "public\\assets\\images\\dragon\\hand-left.png", "size": 123375, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": false, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.400Z", "result": {"SavingsPercent": 85.97771023302938, "OriginalSize": 123375, "OptimizedSize": 17300, "Success": true, "Savings": 106075}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\hand-left.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\frame-top.png", "relativePath": "public\\assets\\images\\dragon\\frame-top.png", "size": 10128, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": false, "canConvertToWebP": true, "lastModified": "2025-04-24T06:17:42.000Z", "result": {"SavingsPercent": 57.64218009478673, "OriginalSize": 10128, "OptimizedSize": 4290, "Success": true, "Savings": 5838}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\frame-top.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\frame-bottom.png", "relativePath": "public\\assets\\images\\dragon\\frame-bottom.png", "size": 8554, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": false, "canConvertToWebP": true, "lastModified": "2025-04-24T06:17:59.000Z", "result": {"SavingsPercent": 55.71662380173018, "OriginalSize": 8554, "OptimizedSize": 3788, "Success": true, "Savings": 4766}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\frame-bottom.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\body-front.png", "relativePath": "public\\assets\\images\\dragon\\body-front.png", "size": 1884422, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": true, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.399Z", "result": {"OriginalSize": 1884422, "QualityAdjusted": true, "OptimizedSize": 107290, "FinalQuality": 70, "SavingsPercent": 94.30647699931332, "Success": true, "Savings": 1777132}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\body-front.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\body-back.png", "relativePath": "public\\assets\\images\\dragon\\body-back.png", "size": 2296993, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": true, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.385Z", "result": {"OriginalSize": 2296993, "QualityAdjusted": true, "OptimizedSize": 131764, "FinalQuality": 70, "SavingsPercent": 94.26363075551384, "Success": true, "Savings": 2165229}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\dragon\\body-back.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\misc\\red-flame.png", "relativePath": "public\\assets\\images\\misc\\red-flame.png", "size": 2089874, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": true, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.459Z", "result": {"OriginalSize": 2089874, "QualityAdjusted": true, "OptimizedSize": 160978, "FinalQuality": 70, "SavingsPercent": 92.29723897230167, "Success": true, "Savings": 1928896}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\misc\\red-flame.webp", "targetFormat": "webp"}, {"path": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\misc\\blue-flame.png", "relativePath": "public\\assets\\images\\misc\\blue-flame.png", "size": 2554542, "extension": ".png", "maxTargetSize": 307200, "needsOptimization": true, "canConvertToWebP": true, "lastModified": "2025-05-21T09:00:12.437Z", "result": {"OriginalSize": 2554542, "QualityAdjusted": true, "OptimizedSize": 183986, "FinalQuality": 70, "SavingsPercent": 92.7976913278388, "Success": true, "Savings": 2370556}, "outputPath": "C:\\Users\\<USER>\\Desktop\\Repositories\\GR\\Roulette-FE\\public\\assets\\images\\misc\\blue-flame.webp", "targetFormat": "webp"}], "errors": [], "config": {"maxFileSizes": {"webp": 153600, "jpeg": 204800, "png": 307200, "gif": 512000, "svg": 51200, "default": 256000}, "quality": {"webp": 85, "jpeg": 85, "png": 95, "default": 85}, "supportedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif", ".webp"], "excludedExtensions": [".svg"], "scanDirectories": ["public/assets/images", "src/assets", "assets"], "excludeDirectories": ["node_modules", "dist", "build", ".git", ".vscode", "coverage"], "excludePatterns": ["*.min.*", "*.optimized.*", "*-backup.*"], "backup": {"enabled": true, "directory": "image-optimization-backups", "timestamp": true, "keepOriginals": true}, "webpConversion": {"enabled": true, "replaceOriginals": false, "updateReferences": true, "fallbackSupport": true}, "codeUpdate": {"enabled": true, "fileExtensions": [".ts", ".tsx", ".js", ".jsx", ".css", ".scss", ".sass"], "excludeFiles": ["node_modules/**", "dist/**", "build/**", "*.min.js", "*.min.css"]}, "reporting": {"enabled": true, "outputFile": "image-optimization-report.json", "includeDetails": true, "showSavings": true}, "imagemagick": {"commonFlags": ["-strip", "-interlace", "Plane", "-gaussian-blur", "0.05", "-colorspace", "sRGB"], "formatFlags": {"jpeg": ["-sampling-factor", "4:2:0", "-define", "jpeg:dct-method=float"], "png": ["-define", "png:compression-filter=5", "-define", "png:compression-level=9", "-define", "png:compression-strategy=1"], "webp": ["-define", "webp:lossless=false", "-define", "webp:method=6", "-define", "webp:alpha-quality=95"]}}, "performance": {"maxConcurrentProcesses": 4, "chunkSize": 10, "timeoutMs": 30000}}}