#!/usr/bin/env node

/**
 * Image Optimization Orchestrator
 * Main script that coordinates the entire image optimization workflow
 */

import fs from "fs/promises"
import path from "path"
import { spawn } from "child_process"
import { glob } from "glob"
import { imageOptimizationConfig } from "./image-optimization/config.js"
import CodeReferenceUpdater from "./image-optimization/update-references.js"

class ImageOptimizer {
  constructor() {
    this.config = imageOptimizationConfig
    this.results = {
      discovered: [],
      analyzed: [],
      optimized: [],
      converted: [],
      errors: [],
      totalSavings: 0,
      startTime: new Date(),
    }
  }

  /**
   * Display help information
   */
  showHelp() {
    console.log("\n🖼️  Image Optimization Workflow")
    console.log("================================\n")

    console.log("Available commands:")
    console.log("  pnpm images:install   - Install ImageMagick CLI tools")
    console.log(
      "  pnpm images:analyze   - Analyze current images without optimization"
    )
    console.log("  pnpm images:convert   - Convert images to WebP format only")
    console.log(
      "  pnpm images:optimize  - Full optimization workflow (recommended)"
    )
    console.log("  pnpm images:help      - Show this help message\n")

    console.log("Full optimization workflow includes:")
    console.log("  • Discovery and analysis of all image assets")
    console.log("  • Compression optimization within size thresholds")
    console.log("  • WebP format conversion for better web performance")
    console.log("  • Automatic code reference updates")
    console.log("  • Backup creation and detailed reporting\n")

    console.log("Configuration:")
    console.log(
      `  • Max WebP size: ${this.formatFileSize(this.config.maxFileSizes.webp)}`
    )
    console.log(
      `  • Max JPEG size: ${this.formatFileSize(this.config.maxFileSizes.jpeg)}`
    )
    console.log(
      `  • Max PNG size: ${this.formatFileSize(this.config.maxFileSizes.png)}`
    )
    console.log(`  • WebP quality: ${this.config.quality.webp}%`)
    console.log(
      `  • Backup enabled: ${this.config.backup.enabled ? "Yes" : "No"}`
    )
    console.log(
      `  • Code updates: ${this.config.codeUpdate.enabled ? "Yes" : "No"}\n`
    )

    console.log("Examples:")
    console.log("  pnpm images:analyze   # See what images would be optimized")
    console.log("  pnpm images:optimize  # Run full optimization process")
    console.log("")
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`
    }
    return `${bytes} bytes`
  }

  /**
   * Check if ImageMagick is installed
   */
  async checkImageMagick() {
    return new Promise((resolve) => {
      const process = spawn("magick", ["-version"], { stdio: "pipe" })
      process.on("close", (code) => resolve(code === 0))
      process.on("error", () => resolve(false))
    })
  }

  /**
   * Discover all image files in the project
   */
  async discoverImages() {
    console.log("🔍 Discovering image files...")

    const allImages = []

    for (const scanDir of this.config.scanDirectories) {
      try {
        const dirExists = await fs
          .access(scanDir)
          .then(() => true)
          .catch(() => false)
        if (!dirExists) continue

        for (const ext of this.config.supportedExtensions) {
          const pattern = path.join(scanDir, `**/*${ext}`).replace(/\\/g, "/")
          const files = await glob(pattern, {
            ignore: this.config.excludeDirectories.map((dir) => `${dir}/**`),
            absolute: true,
          })
          allImages.push(...files)
        }
      } catch (error) {
        this.results.errors.push(
          `Failed to scan directory ${scanDir}: ${error.message}`
        )
      }
    }

    // Remove duplicates and filter out excluded patterns
    const uniqueImages = [...new Set(allImages)].filter((imagePath) => {
      const fileName = path.basename(imagePath)
      return !this.config.excludePatterns.some((pattern) => {
        const regex = new RegExp(pattern.replace(/\*/g, ".*"))
        return regex.test(fileName)
      })
    })

    this.results.discovered = uniqueImages
    console.log(`📁 Found ${uniqueImages.length} image files`)

    return uniqueImages
  }

  /**
   * Analyze images and determine optimization potential
   */
  async analyzeImages(imagePaths) {
    console.log("📊 Analyzing images...")

    const analyzed = []

    for (const imagePath of imagePaths) {
      try {
        const stats = await fs.stat(imagePath)
        const ext = path.extname(imagePath).toLowerCase()
        const maxSize =
          this.config.maxFileSizes[ext.slice(1)] ||
          this.config.maxFileSizes.default

        const analysis = {
          path: imagePath,
          relativePath: path.relative(process.cwd(), imagePath),
          size: stats.size,
          extension: ext,
          maxTargetSize: maxSize,
          needsOptimization: stats.size > maxSize,
          canConvertToWebP: ![".webp", ".svg"].includes(ext),
          lastModified: stats.mtime,
        }

        analyzed.push(analysis)
      } catch (error) {
        this.results.errors.push(
          `Failed to analyze ${imagePath}: ${error.message}`
        )
      }
    }

    this.results.analyzed = analyzed

    // Display analysis summary
    const needsOptimization = analyzed.filter((img) => img.needsOptimization)
    const canConvert = analyzed.filter((img) => img.canConvertToWebP)
    const totalSize = analyzed.reduce((sum, img) => sum + img.size, 0)

    console.log(`\n📈 Analysis Summary:`)
    console.log(`   Total images: ${analyzed.length}`)
    console.log(`   Total size: ${this.formatFileSize(totalSize)}`)
    console.log(`   Need optimization: ${needsOptimization.length}`)
    console.log(`   Can convert to WebP: ${canConvert.length}`)

    if (needsOptimization.length > 0) {
      console.log(`\n🔧 Images that need optimization:`)
      needsOptimization.slice(0, 10).forEach((img) => {
        console.log(
          `   • ${img.relativePath} (${this.formatFileSize(
            img.size
          )} > ${this.formatFileSize(img.maxTargetSize)})`
        )
      })
      if (needsOptimization.length > 10) {
        console.log(`   ... and ${needsOptimization.length - 10} more`)
      }
    }

    return analyzed
  }

  /**
   * Execute PowerShell script for image optimization
   */
  async executeOptimization(inputPath, outputPath, format, quality, maxSize) {
    return new Promise((resolve, reject) => {
      const scriptPath = path.join(
        process.cwd(),
        "scripts/image-optimization/optimize-images.ps1"
      )
      const args = [
        "-ExecutionPolicy",
        "Bypass",
        "-File",
        scriptPath,
        "-InputPath",
        inputPath,
        "-OutputPath",
        outputPath,
        "-Format",
        format,
        "-Quality",
        quality.toString(),
        "-MaxSize",
        maxSize.toString(),
      ]

      if (this.config.backup.enabled) {
        args.push("-Backup", "-BackupDir", this.config.backup.directory)
      }

      const optimizeProcess = spawn("powershell", args, { stdio: "pipe" })
      let output = ""
      let error = ""

      optimizeProcess.stdout.on("data", (data) => {
        output += data.toString()
      })

      optimizeProcess.stderr.on("data", (data) => {
        error += data.toString()
      })

      optimizeProcess.on("close", (code) => {
        if (code === 0) {
          // Parse optimization result from output
          const resultMatch = output.match(/OPTIMIZATION_RESULT:(.+)/)
          if (resultMatch) {
            try {
              const result = JSON.parse(resultMatch[1])
              resolve(result)
            } catch (parseError) {
              resolve({ Success: true, output })
            }
          } else {
            resolve({ Success: true, output })
          }
        } else {
          reject(new Error(`PowerShell script failed: ${error || output}`))
        }
      })

      optimizeProcess.on("error", (err) => {
        reject(new Error(`Failed to execute PowerShell script: ${err.message}`))
      })
    })
  }

  /**
   * Run the main optimization workflow
   */
  async runOptimization() {
    console.log("🚀 Starting image optimization workflow...\n")

    // Check ImageMagick installation
    if (!(await this.checkImageMagick())) {
      console.log("❌ ImageMagick not found. Please run: pnpm images:install")
      return false
    }

    // Discover and analyze images
    const imagePaths = await this.discoverImages()
    if (imagePaths.length === 0) {
      console.log("ℹ️  No images found to optimize")
      return true
    }

    const analyzed = await this.analyzeImages(imagePaths)
    const toOptimize = analyzed.filter(
      (img) => img.needsOptimization || img.canConvertToWebP
    )

    if (toOptimize.length === 0) {
      console.log("✅ All images are already optimized!")
      return true
    }

    console.log(`\n🔧 Optimizing ${toOptimize.length} images...\n`)

    // Process images
    const imageMapping = {}
    let processed = 0

    for (const imageInfo of toOptimize) {
      try {
        processed++
        console.log(
          `[${processed}/${toOptimize.length}] Processing: ${imageInfo.relativePath}`
        )

        const inputPath = imageInfo.path
        const ext = imageInfo.extension
        const targetFormat = imageInfo.canConvertToWebP ? "webp" : ext.slice(1)
        const quality =
          this.config.quality[targetFormat] || this.config.quality.default
        const maxSize =
          this.config.maxFileSizes[targetFormat] ||
          this.config.maxFileSizes.default

        // Determine output path
        let outputPath = inputPath
        if (targetFormat === "webp" && ext !== ".webp") {
          outputPath = inputPath.replace(ext, ".webp")
          imageMapping[imageInfo.relativePath] = path.relative(
            process.cwd(),
            outputPath
          )
        }

        // Execute optimization
        const result = await this.executeOptimization(
          inputPath,
          outputPath,
          targetFormat,
          quality,
          maxSize
        )

        if (result.Success) {
          this.results.optimized.push({
            ...imageInfo,
            result,
            outputPath,
            targetFormat,
          })

          if (result.Savings) {
            this.results.totalSavings += result.Savings
          }
        } else {
          this.results.errors.push(
            `Failed to optimize ${imageInfo.relativePath}: ${result.Error}`
          )
        }
      } catch (error) {
        this.results.errors.push(
          `Error processing ${imageInfo.relativePath}: ${error.message}`
        )
      }
    }

    // Update code references if enabled
    if (
      this.config.codeUpdate.enabled &&
      Object.keys(imageMapping).length > 0
    ) {
      console.log("\n📝 Updating code references...")
      const updater = new CodeReferenceUpdater(this.config.codeUpdate)
      const updateResult = await updater.updateAllReferences(imageMapping)

      console.log(
        `✅ Updated ${updateResult.totalUpdates} references in ${updateResult.updatedFiles} files`
      )
    }

    // Generate report
    await this.generateReport()

    console.log("\n🎉 Image optimization completed!")
    console.log(`   Processed: ${this.results.optimized.length} images`)
    console.log(
      `   Total savings: ${this.formatFileSize(this.results.totalSavings)}`
    )
    console.log(`   Errors: ${this.results.errors.length}`)

    if (this.results.errors.length > 0) {
      console.log("\n❌ Errors encountered:")
      this.results.errors.forEach((error) => console.log(`   • ${error}`))
    }

    return true
  }

  /**
   * Generate detailed optimization report
   */
  async generateReport() {
    if (!this.config.reporting.enabled) return

    const report = {
      summary: {
        startTime: this.results.startTime,
        endTime: new Date(),
        totalImages: this.results.discovered.length,
        analyzedImages: this.results.analyzed.length,
        optimizedImages: this.results.optimized.length,
        totalSavings: this.results.totalSavings,
        errors: this.results.errors.length,
      },
      optimized: this.results.optimized,
      errors: this.results.errors,
      config: this.config,
    }

    try {
      await fs.writeFile(
        this.config.reporting.outputFile,
        JSON.stringify(report, null, 2)
      )
      console.log(`📄 Report saved: ${this.config.reporting.outputFile}`)
    } catch (error) {
      console.log(`⚠️  Failed to save report: ${error.message}`)
    }
  }
}

// Main execution
const args = process.argv.slice(2)
const command = args[0] || "optimize"

const optimizer = new ImageOptimizer()

switch (command) {
  case "help":
    optimizer.showHelp()
    break

  case "analyze":
    console.log("🔍 Running analysis only...")
    optimizer
      .discoverImages()
      .then((images) => optimizer.analyzeImages(images))
      .then(() => console.log("\n✅ Analysis complete"))
      .catch((error) => {
        console.error("❌ Analysis failed:", error.message)
        process.exit(1)
      })
    break

  case "convert":
    console.log("🔄 Running WebP conversion only...")
    // Implementation for conversion-only mode would go here
    console.log(
      '⚠️  Convert-only mode not yet implemented. Use "optimize" for full workflow.'
    )
    break

  case "optimize":
  default:
    optimizer
      .runOptimization()
      .then((success) => {
        if (!success) process.exit(1)
      })
      .catch((error) => {
        console.error("❌ Optimization failed:", error.message)
        process.exit(1)
      })
    break
}
