import { SVGIcon } from "@/hooks"
import { useGameStateStore } from "@/stores/game-state-store"
import { useSettingsStore } from "@/stores/settings-store"

type ControlButtonProps = {
  onClick: () => void
  iconUrl: string
  ariaLabel?: string
}

/**
 * Reusable control button component for increment/decrement actions
 */
const ControlButton = ({ onClick, iconUrl, ariaLabel }: ControlButtonProps) => (
  <button
    onClick={onClick}
    aria-label={ariaLabel}
    className='aspect-square hover:scale-105 ease-in-out duration-200 transition-all flex justify-center items-center rounded-md border-2 border-[#c69e61] bg-zinc-800 w-full text-white'
  >
    <SVGIcon url={iconUrl} className='h-4 w-4' />
  </button>
)

/**
 * Display and control for neighbours count in roulette game
 */
export const NeighboursButtons = () => {
  const { initialNeighbours, setInitialNeighbours } = useSettingsStore()
  const gameType = useGameStateStore((state) => state.gameType)

  // Only show in non-normal game types
  if (gameType === "normal") return null

  const decrementNeighbours = () => {
    if (initialNeighbours > 0) {
      setInitialNeighbours(initialNeighbours - 1)
    }
  }

  const incrementNeighbours = () => {
    setInitialNeighbours(initialNeighbours + 1)
  }

  return (
    <div className='flex md:flex-col items-center justify-center space-y-2 min-w-8'>
      <ControlButton
        onClick={decrementNeighbours}
        iconUrl='/assets/svgs/minus.svg'
        ariaLabel='Decrease neighbours'
      />

      <span className='rounded-md border-2 border-[#c69e61] text-center bg-zinc-800 w-full text-lg font-bold lg:text-2xl'>
        {initialNeighbours}
      </span>

      <ControlButton
        onClick={incrementNeighbours}
        iconUrl='/assets/svgs/plus.svg'
        ariaLabel='Increase neighbours'
      />
    </div>
  )
}
