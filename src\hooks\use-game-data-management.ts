import { useEffect, useRef, useState } from "react"
import { getHistory, getWinn<PERSON>, History } from "@/hooks"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import { LAYOUT_CONSTANTS } from "../components/online/layout-constants"
import { tryCatch } from "@maxmorozoff/try-catch-tuple"

interface GameDataManagementState {
  gameHistoryResults: History[]
  isWinningsCalculationInProgress: boolean
}

interface GameDataManagementActions {
  refreshGameHistory: () => Promise<void>
  calculateCurrentWinnings: () => Promise<void>
}

export const useGameDataManagement = (): GameDataManagementState &
  GameDataManagementActions => {
  const [gameHistoryResults, setGameHistoryResults] = useState<History[]>([])
  const [isWinningsCalculationInProgress, setIsWinningsCalculationInProgress] =
    useState(false)

  const lastProcessedRoundIdRef = useRef<string | null>(null)

  const roundData = useGameStateStore((state) => state.roundData)
  const { setWinnings } = useBettingStore()
  const betslipId = useBettingStore((state) => state.betslipId)

  const refreshGameHistory = async (): Promise<void> => {
    if (!roundData) return
    const [fetchedHistory, error] = await tryCatch(
      () => getHistory(LAYOUT_CONSTANTS.HISTORY_FETCH_LIMIT),
      "Refresh Game History"
    )
    const reversedHistoryForDisplay = fetchedHistory
      ? fetchedHistory.reverse()
      : []

    if (error) {
      console.error("Failed to refresh game history:", error)
      return
    }
    setGameHistoryResults(reversedHistoryForDisplay)
  }

  const calculateCurrentWinnings = async (): Promise<void> => {
    if (!betslipId || betslipId === 0) {
      setWinnings(0)
      return
    }

    setIsWinningsCalculationInProgress(true)

    const [calculatedWinnings, error] = await tryCatch(
      () => getWinnings(betslipId),
      "Calculate Winnings"
    )

    if (error) {
      console.error("Failed to calculate winnings:", error)
      setWinnings(0)
      setIsWinningsCalculationInProgress(false)
      return
    }

    setWinnings(calculatedWinnings)
  }

  const shouldProcessNewRound = (): boolean => {
    if (!roundData?.rouletteNumber || !roundData?.bonusNumber) {
      return false
    }

    const currentRoundId = roundData.rouletteDrawId?.toString()

    if (!currentRoundId || currentRoundId === lastProcessedRoundIdRef.current) {
      return false
    }

    return true
  }

  const markRoundAsProcessed = (): void => {
    const currentRoundId = roundData?.rouletteDrawId?.toString()
    if (currentRoundId) {
      lastProcessedRoundIdRef.current = currentRoundId
    }
  }

  useEffect(() => {
    if (shouldProcessNewRound()) {
      markRoundAsProcessed()
    }
  }, [roundData])

  useEffect(() => {
    refreshGameHistory()
  }, [roundData])

  return {
    gameHistoryResults,
    isWinningsCalculationInProgress,
    refreshGameHistory,
    calculateCurrentWinnings,
  }
}
