#!/usr/bin/env node

/**
 * ImageMagick Installation Script (Node.js version)
 * A more reliable alternative to the PowerShell script
 */

import { spawn } from "child_process"
import { promisify } from "util"

const execAsync = promisify(spawn)

class ImageMagickInstaller {
  constructor() {
    this.isWindows = process.platform === "win32"
  }

  /**
   * Check if running as administrator on Windows
   */
  async isRunningAsAdmin() {
    if (!this.isWindows) return true // Non-Windows systems don't need this check

    return new Promise((resolve) => {
      const adminCheckProcess = spawn(
        "powershell",
        [
          "-Command",
          '([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")',
        ],
        { stdio: "pipe" }
      )

      let output = ""
      adminCheckProcess.stdout.on("data", (data) => {
        output += data.toString().trim()
      })

      adminCheckProcess.on("close", () => {
        resolve(output.toLowerCase() === "true")
      })

      adminCheckProcess.on("error", () => resolve(false))
    })
  }

  /**
   * Restart the script with administrator privileges
   */
  async elevateToAdmin() {
    console.log("🔐 Requesting administrator privileges...")
    console.log('ℹ️  A UAC prompt will appear - please click "Yes" to continue')

    return new Promise((resolve, reject) => {
      // Get the current script path and arguments
      const scriptPath = process.argv[1]
      const args = process.argv.slice(2)

      // Create PowerShell command to run Node.js with admin privileges
      const argList = [`"${scriptPath}"`]
        .concat(args.map((arg) => `"${arg}"`))
        .join(", ")
      const elevateCommand = `Start-Process -FilePath "node" -ArgumentList ${argList} -Verb RunAs -Wait`

      const elevateProcess = spawn("powershell", ["-Command", elevateCommand], {
        stdio: "inherit",
      })

      elevateProcess.on("close", (code) => {
        if (code === 0) {
          console.log("✅ Installation completed with administrator privileges")
          resolve(true)
        } else {
          reject(new Error("Failed to run with administrator privileges"))
        }
      })

      elevateProcess.on("error", (error) => {
        reject(new Error(`Failed to elevate privileges: ${error.message}`))
      })
    })
  }

  /**
   * Check if ImageMagick is installed
   */
  async checkImageMagick() {
    return new Promise((resolve) => {
      const process = spawn("magick", ["-version"], { stdio: "pipe" })

      let output = ""
      process.stdout.on("data", (data) => {
        output += data.toString()
      })

      process.on("close", (code) => {
        const isInstalled = code === 0 && output.includes("ImageMagick")
        resolve(isInstalled)
      })

      process.on("error", () => resolve(false))
    })
  }

  /**
   * Check if Chocolatey is installed
   */
  async checkChocolatey() {
    return new Promise((resolve) => {
      // Try multiple ways to detect Chocolatey
      const process = spawn("choco", ["--version"], { stdio: "pipe" })

      process.on("close", (code) => {
        if (code === 0) {
          resolve(true)
        } else {
          // If choco command failed, check if Chocolatey directory exists
          this.checkChocolateyDirectory().then(resolve)
        }
      })

      process.on("error", () => {
        // If choco command not found, check if Chocolatey directory exists
        this.checkChocolateyDirectory().then(resolve)
      })
    })
  }

  /**
   * Check if Chocolatey directory exists (fallback detection)
   */
  async checkChocolateyDirectory() {
    const fs = await import("fs/promises")

    const possiblePaths = [
      "C:\\ProgramData\\chocolatey",
      "C:\\Chocolatey",
      process.env.ChocolateyInstall,
    ].filter(Boolean)

    for (const chocoPath of possiblePaths) {
      try {
        await fs.access(chocoPath)
        console.log(`ℹ️  Found Chocolatey installation at: ${chocoPath}`)
        return true
      } catch {
        // Continue checking other paths
      }
    }

    return false
  }

  /**
   * Install Chocolatey
   */
  async installChocolatey() {
    console.log("🔧 Installing Chocolatey package manager...")

    if (!this.isWindows) {
      throw new Error("Chocolatey installation is only supported on Windows")
    }

    return new Promise((resolve, reject) => {
      const installScript = `
        Set-ExecutionPolicy Bypass -Scope Process -Force;
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072;
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
      `

      const process = spawn(
        "powershell",
        ["-ExecutionPolicy", "Bypass", "-Command", installScript],
        { stdio: "inherit" }
      )

      process.on("close", (code) => {
        if (code === 0) {
          console.log("✅ Chocolatey installed successfully!")
          resolve(true)
        } else {
          reject(
            new Error(`Chocolatey installation failed with exit code ${code}`)
          )
        }
      })

      process.on("error", (error) => {
        reject(
          new Error(
            `Failed to execute Chocolatey installation: ${error.message}`
          )
        )
      })
    })
  }

  /**
   * Get Chocolatey executable path
   */
  async getChocolateyPath() {
    // Try common Chocolatey installation paths
    const possiblePaths = [
      "C:\\ProgramData\\chocolatey\\bin\\choco.exe",
      "C:\\Chocolatey\\bin\\choco.exe",
      process.env.ChocolateyInstall
        ? `${process.env.ChocolateyInstall}\\bin\\choco.exe`
        : null,
    ].filter(Boolean)

    const fs = await import("fs/promises")

    for (const chocoPath of possiblePaths) {
      try {
        await fs.access(chocoPath)
        return chocoPath
      } catch {
        // Continue checking other paths
      }
    }

    // Fallback to just 'choco' and hope it's in PATH
    return "choco"
  }

  /**
   * Install ImageMagick via Chocolatey
   */
  async installImageMagick() {
    console.log("🖼️  Installing ImageMagick via Chocolatey...")

    const chocoPath = await this.getChocolateyPath()
    console.log(`ℹ️  Using Chocolatey at: ${chocoPath}`)

    return new Promise((resolve, reject) => {
      const process = spawn(chocoPath, ["install", "imagemagick", "-y"], {
        stdio: "inherit",
      })

      process.on("close", (code) => {
        if (code === 0) {
          console.log("✅ ImageMagick installed successfully!")
          resolve(true)
        } else {
          reject(
            new Error(`ImageMagick installation failed with exit code ${code}`)
          )
        }
      })

      process.on("error", (error) => {
        reject(
          new Error(
            `Failed to execute ImageMagick installation: ${error.message}`
          )
        )
      })
    })
  }

  /**
   * Show ImageMagick version information
   */
  async showImageMagickInfo() {
    return new Promise((resolve) => {
      const process = spawn("magick", ["-version"], { stdio: "inherit" })

      process.on("close", () => {
        console.log("\n🎉 ImageMagick is ready for image optimization!")
        resolve()
      })

      process.on("error", () => {
        console.log("⚠️  Could not retrieve ImageMagick version information.")
        resolve()
      })
    })
  }

  /**
   * Main installation workflow
   */
  async install(options = {}) {
    const { force = false, checkOnly = false } = options

    console.log("\n🖼️  ImageMagick Installation")
    console.log("============================\n")

    try {
      // Check current installation status
      const isInstalled = await this.checkImageMagick()

      if (checkOnly) {
        if (isInstalled) {
          console.log("✅ ImageMagick is already installed and available.")
          await this.showImageMagickInfo()
          return true
        } else {
          console.log(
            "❌ ImageMagick is not installed or not available in PATH."
          )
          return false
        }
      }

      if (isInstalled && !force) {
        console.log("✅ ImageMagick is already installed!")
        await this.showImageMagickInfo()
        return true
      }

      if (force && isInstalled) {
        console.log("🔄 Force flag specified. Reinstalling ImageMagick...")
      }

      // Check if we're on Windows
      if (!this.isWindows) {
        console.log("❌ This installer currently only supports Windows.")
        console.log(
          "ℹ️  Please install ImageMagick manually for your operating system:"
        )
        console.log("   • macOS: brew install imagemagick")
        console.log("   • Ubuntu/Debian: sudo apt-get install imagemagick")
        console.log("   • Other: https://imagemagick.org/script/download.php")
        return false
      }

      // Check if running as administrator
      const isAdmin = await this.isRunningAsAdmin()
      if (!isAdmin) {
        console.log("⚠️  Administrator privileges required for installation.")
        console.log("🔐 Attempting to restart with administrator privileges...")

        try {
          await this.elevateToAdmin()
          // If we reach here, the elevated process completed successfully
          return true
        } catch (error) {
          console.log("❌ Failed to elevate privileges:", error.message)
          console.log("\n📋 Manual Steps:")
          console.log("1. Right-click on PowerShell or Command Prompt")
          console.log("2. Select 'Run as administrator'")
          console.log("3. Navigate to your project directory")
          console.log("4. Run: pnpm images:install")
          return false
        }
      }

      console.log("✅ Running with administrator privileges")

      // Check Chocolatey installation
      const hasChocolatey = await this.checkChocolatey()

      if (!hasChocolatey) {
        console.log("⚠️  Chocolatey package manager not found.")
        console.log("ℹ️  Installing Chocolatey first...")

        try {
          await this.installChocolatey()
        } catch (error) {
          console.log("❌ Failed to install Chocolatey:", error.message)
          console.log(
            "ℹ️  Please install Chocolatey manually: https://chocolatey.org/install"
          )
          return false
        }
      }

      // Install ImageMagick
      try {
        await this.installImageMagick()

        // Verify installation
        const isNowInstalled = await this.checkImageMagick()
        if (isNowInstalled) {
          console.log("\n🎉 Installation completed successfully!")
          await this.showImageMagickInfo()

          console.log("\n📋 Next Steps:")
          console.log(
            '• Run "pnpm images:analyze" to analyze your current images'
          )
          console.log(
            '• Run "pnpm images:optimize" to start the optimization process'
          )
          console.log('• Run "pnpm images:help" for more options')

          return true
        } else {
          console.log(
            "❌ Installation completed but ImageMagick is not available in PATH."
          )
          console.log(
            "ℹ️  You may need to restart your terminal or add ImageMagick to your PATH manually."
          )
          return false
        }
      } catch (error) {
        console.log("❌ Failed to install ImageMagick:", error.message)
        return false
      }
    } catch (error) {
      console.log("❌ Installation failed:", error.message)
      return false
    }
  }
}

// CLI usage - check if this file is being run directly
const isMainModule =
  process.argv[1] && process.argv[1].endsWith("install-imagemagick.js")

if (isMainModule) {
  const args = process.argv.slice(2)
  const force = args.includes("--force")
  const checkOnly = args.includes("--check-only")

  const installer = new ImageMagickInstaller()

  installer
    .install({ force, checkOnly })
    .then((success) => {
      process.exit(success ? 0 : 1)
    })
    .catch((error) => {
      console.error("❌ Installation error:", error.message)
      process.exit(1)
    })
}

export default ImageMagickInstaller
