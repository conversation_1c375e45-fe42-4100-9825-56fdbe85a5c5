#!/usr/bin/env node

/**
 * Image Optimization Configuration
 * Centralized configuration for the image optimization workflow
 */

export const imageOptimizationConfig = {
  // File size thresholds (in bytes)
  maxFileSizes: {
    // Web-optimized size limits for different image types
    webp: 150 * 1024,    // 150KB for WebP (excellent compression)
    jpeg: 200 * 1024,    // 200KB for JPEG (good for photos)
    png: 300 * 1024,     // 300KB for PNG (good for graphics with transparency)
    gif: 500 * 1024,     // 500KB for GIF (animations need more space)
    svg: 50 * 1024,      // 50KB for SVG (text-based, should be small)
    default: 250 * 1024  // 250KB default for other formats
  },

  // Image quality settings for compression
  quality: {
    webp: 85,      // High quality WebP
    jpeg: 85,      // High quality JPEG
    png: 95,       // PNG compression level (0-100, higher = better quality)
    default: 85    // Default quality for other formats
  },

  // Supported image extensions for processing
  supportedExtensions: [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'
  ],

  // Extensions to exclude from processing
  excludedExtensions: [
    '.svg'  // SVG files are vector-based and don't need raster optimization
  ],

  // Directories to scan for images
  scanDirectories: [
    'public/assets/images',
    'src/assets',
    'assets'
  ],

  // Directories to exclude from scanning
  excludeDirectories: [
    'node_modules',
    'dist',
    'build',
    '.git',
    '.vscode',
    'coverage'
  ],

  // File patterns to exclude
  excludePatterns: [
    '*.min.*',      // Already minified files
    '*.optimized.*', // Already optimized files
    '*-backup.*'    // Backup files
  ],

  // Backup settings
  backup: {
    enabled: true,
    directory: 'image-optimization-backups',
    timestamp: true,
    keepOriginals: true
  },

  // WebP conversion settings
  webpConversion: {
    enabled: true,
    replaceOriginals: false,  // Keep originals alongside WebP versions
    updateReferences: true,   // Update code references to use WebP
    fallbackSupport: true     // Maintain fallback to original formats
  },

  // Code reference update settings
  codeUpdate: {
    enabled: true,
    fileExtensions: ['.ts', '.tsx', '.js', '.jsx', '.css', '.scss', '.sass'],
    excludeFiles: [
      'node_modules/**',
      'dist/**',
      'build/**',
      '*.min.js',
      '*.min.css'
    ]
  },

  // Reporting settings
  reporting: {
    enabled: true,
    outputFile: 'image-optimization-report.json',
    includeDetails: true,
    showSavings: true
  },

  // ImageMagick command settings
  imagemagick: {
    // Common optimization flags
    commonFlags: [
      '-strip',           // Remove metadata
      '-interlace', 'Plane', // Progressive loading
      '-gaussian-blur', '0.05', // Slight blur for better compression
      '-colorspace', 'sRGB'  // Ensure consistent color space
    ],
    
    // Format-specific optimization flags
    formatFlags: {
      jpeg: [
        '-sampling-factor', '4:2:0',  // Chroma subsampling
        '-define', 'jpeg:dct-method=float'
      ],
      png: [
        '-define', 'png:compression-filter=5',
        '-define', 'png:compression-level=9',
        '-define', 'png:compression-strategy=1'
      ],
      webp: [
        '-define', 'webp:lossless=false',
        '-define', 'webp:method=6',
        '-define', 'webp:alpha-quality=95'
      ]
    }
  },

  // Performance settings
  performance: {
    maxConcurrentProcesses: 4,  // Number of parallel image processing tasks
    chunkSize: 10,              // Number of images to process in each batch
    timeoutMs: 30000           // Timeout for individual image processing (30 seconds)
  }
}

export default imageOptimizationConfig
