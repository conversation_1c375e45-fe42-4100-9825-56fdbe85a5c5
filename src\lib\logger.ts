import * as fs from "fs"
import localforage from "localforage"

/**
 * Simplified Logging Utility
 *
 * Features:
 * - Console and file logging only
 * - Always-on behavior with consistent configuration
 * - LocalForage for reliable storage management
 * - Maintained backward compatibility with existing APIs
 */

type LogLevel = {
  value: number
  label: string
}

type LogMetadata = {
  timestamp?: Date
  source?: string
  component?: string
  event?: string
  render?: string
  [key: string]: any
}

// Configure localforage for log storage
localforage.config({
  name: "Rou<PERSON><PERSON><PERSON>",
  storeName: "logs",
  description: "Application logs storage",
})

class Logger {
  // Log levels with numeric values for comparison
  static LOG_LEVELS = {
    DEBUG: { value: 0, label: "DEBUG" } as LogLevel,
    INFO: { value: 1, label: "INFO" } as LogLevel,
    LOG: { value: 2, label: "LOG" } as LogLevel,
    WARN: { value: 3, label: "WARN" } as LogLevel,
    ERROR: { value: 4, label: "ERROR" } as LogLevel,
  }

  // Simplified configuration - always-on behavior
  private readonly filePath: string = "./app.log.txt"
  private readonly includeTimestamp: boolean = true
  private readonly minLevel: LogLevel = Logger.LOG_LEVELS.DEBUG

  constructor() {
    // No configuration needed - always-on behavior
  }

  /**
   * Format log message with timestamp and metadata
   */
  private formatMessage(
    level: LogLevel,
    message: string,
    metadata: LogMetadata
  ): string {
    const timestamp = this.includeTimestamp
      ? `[${new Date().toISOString()}] `
      : ""
    const source = metadata.source ? `[${metadata.source}] ` : ""
    const component = metadata.component
      ? `[Component: ${metadata.component}] `
      : ""
    const event = metadata.event ? `[Event: ${metadata.event}] ` : ""
    const render = metadata.render ? `[Render: ${metadata.render}] ` : ""

    return `${timestamp}${level.label} ${source}${component}${event}${render}${message}`
  }

  /**
   * Internal logging method that handles all log processing
   */
  private _log(
    level: LogLevel,
    message: string,
    metadata: LogMetadata = {}
  ): void {
    // Check if this log should be processed based on minimum level
    if (level.value < this.minLevel.value) {
      return
    }

    // Add timestamp to metadata
    const mergedMetadata = {
      ...metadata,
      timestamp: new Date(),
    }

    // Format the log message
    const formattedLog = this.formatMessage(level, message, mergedMetadata)

    // Always log to console (synchronous)
    this.logToConsole(level, formattedLog)

    // Log to file (asynchronous, fire-and-forget)
    this.logToFile(formattedLog).catch(() => {
      // Silently ignore file logging errors to prevent recursion
    })
  }

  /**
   * Log to console with appropriate console method
   */
  private logToConsole(level: LogLevel, formattedLog: string): void {
    switch (level) {
      case Logger.LOG_LEVELS.DEBUG:
        console.debug(formattedLog)
        break
      case Logger.LOG_LEVELS.INFO:
      case Logger.LOG_LEVELS.LOG:
        console.log(formattedLog)
        break
      case Logger.LOG_LEVELS.WARN:
        console.warn(formattedLog)
        break
      case Logger.LOG_LEVELS.ERROR:
        console.error(formattedLog)
        break
    }
  }

  /**
   * Log to file using localForage for browser and fs for Node.js
   */
  private async logToFile(formattedLog: string): Promise<void> {
    try {
      // In browser environment, use localForage for reliable storage
      if (typeof window !== "undefined") {
        await this.writeToLocalForage(formattedLog)
        return
      }

      // In Node.js environment, write to file in project root
      const filePath = this.filePath
      fs.appendFileSync(filePath, formattedLog + "\n")
    } catch (error) {
      // Fallback to console only if file logging fails
      if (error instanceof Error) {
        console.error("Failed to write log to file:", error.message)
      }
    }
  }

  /**
   * Write log to localForage with automatic cleanup
   */
  private async writeToLocalForage(formattedLog: string): Promise<void> {
    try {
      const LOG_KEY = "app_logs"
      const MAX_LOGS = 1000 // Keep last 1000 log entries

      // Get existing logs
      const existingLogs: string[] = (await localforage.getItem(LOG_KEY)) || []

      // Add new log entry
      const updatedLogs = [...existingLogs, formattedLog]

      // Keep only the most recent logs
      const trimmedLogs = updatedLogs.slice(-MAX_LOGS)

      // Store back to localForage
      await localforage.setItem(LOG_KEY, trimmedLogs)
    } catch (error) {
      // If localForage fails, fall back to console only
      if (error instanceof Error) {
        console.error("Failed to write to localForage:", error.message)
      }
    }
  }

  /**
   * Convenience methods for different log levels
   */
  debug(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.DEBUG, message, metadata)
  }

  info(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.INFO, message, metadata)
  }

  log(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.LOG, message, metadata)
  }

  warn(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.WARN, message, metadata)
  }

  error(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.ERROR, message, metadata)
  }

  /**
   * Log JavaScript errors with automatic stack trace extraction
   */
  logError(
    error: Error,
    additionalMessage?: string,
    metadata: LogMetadata = {}
  ): void {
    const message = additionalMessage
      ? `${additionalMessage}: ${error.message}`
      : error.message

    this._log(Logger.LOG_LEVELS.ERROR, message, {
      ...metadata,
      error,
      stack: error.stack,
    })
  }

  /**
   * Create a logger for a specific component
   */
  forComponent(componentName: string) {
    return {
      debug: (message: string, metadata = {}) =>
        this.debug(message, { ...metadata, component: componentName }),
      info: (message: string, metadata = {}) =>
        this.info(message, { ...metadata, component: componentName }),
      log: (message: string, metadata = {}) =>
        this.log(message, { ...metadata, component: componentName }),
      warn: (message: string, metadata = {}) =>
        this.warn(message, { ...metadata, component: componentName }),
      error: (message: string, metadata = {}) =>
        this.error(message, { ...metadata, component: componentName }),
      logError: (error: Error, additionalMessage?: string, metadata = {}) =>
        this.logError(error, additionalMessage, {
          ...metadata,
          component: componentName,
        }),

      // Additional helpers for event and render logging
      logEvent: (eventName: string, message: string, metadata = {}) => {
        this.info(message, {
          ...metadata,
          component: componentName,
          event: eventName,
        })
      },

      logRender: (renderInfo: string, metadata = {}) => {
        this.debug(`Component rendered`, {
          ...metadata,
          component: componentName,
          render: renderInfo,
        })
      },
    }
  }

  /**
   * Create a logger for a specific source (module, service, etc.)
   */
  forSource(sourceName: string) {
    return {
      debug: (message: string, metadata = {}) =>
        this.debug(message, { ...metadata, source: sourceName }),
      info: (message: string, metadata = {}) =>
        this.info(message, { ...metadata, source: sourceName }),
      log: (message: string, metadata = {}) =>
        this.log(message, { ...metadata, source: sourceName }),
      warn: (message: string, metadata = {}) =>
        this.warn(message, { ...metadata, source: sourceName }),
      error: (message: string, metadata = {}) =>
        this.error(message, { ...metadata, source: sourceName }),
      logError: (error: Error, additionalMessage?: string, metadata = {}) =>
        this.logError(error, additionalMessage, {
          ...metadata,
          source: sourceName,
        }),
    }
  }

  /**
   * Clean up resources - simplified for new implementation
   */
  dispose() {
    // No cleanup needed for simplified logger
  }
}

export default Logger

/**
 * Compatibility layer for the middleware logger
 * This provides the same interface as the middleware logger
 * but uses the new Logger class internally
 */

/**
 * Performance-optimized logger that prevents React re-renders
 * by deferring all logging operations to the next tick
 */
class PerformantLogger {
  private loggerInstance: any
  private logQueue: Array<() => void> = []
  private isProcessing = false

  constructor(loggerInstance: any) {
    this.loggerInstance = loggerInstance
  }

  private queueLog(logFn: () => void) {
    this.logQueue.push(logFn)

    if (!this.isProcessing) {
      this.isProcessing = true
      // Use setTimeout to defer logging to next tick, preventing re-renders
      setTimeout(() => {
        this.processQueue()
      }, 0)
    }
  }

  private processQueue() {
    while (this.logQueue.length > 0) {
      const logFn = this.logQueue.shift()
      if (logFn) {
        try {
          logFn()
        } catch (error) {
          // Fallback to console to avoid recursive issues
          console.error("Logger queue processing error:", error)
        }
      }
    }
    this.isProcessing = false
  }

  debug(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.debug(message, metadata))
  }

  info(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.info(message, metadata))
  }

  log(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.log(message, metadata))
  }

  warn(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.warn(message, metadata))
  }

  error(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.error(message, metadata))
  }

  logError(error: Error, additionalMessage?: string, metadata = {}) {
    this.queueLog(() =>
      this.loggerInstance.logError(error, additionalMessage, metadata)
    )
  }
}

/**
 * Utility function to check localForage storage health
 */
export const checkStorageHealth = async (): Promise<{
  logCount: number
  canWrite: boolean
}> => {
  try {
    const LOG_KEY = "app_logs"

    // Get current logs
    const logs: string[] = (await localforage.getItem(LOG_KEY)) || []

    // Test if we can write
    let canWrite = true
    try {
      await localforage.setItem("__test__", "test")
      await localforage.removeItem("__test__")
    } catch (error) {
      canWrite = false
    }

    return {
      logCount: logs.length,
      canWrite,
    }
  } catch (error) {
    return {
      logCount: 0,
      canWrite: false,
    }
  }
}

// Create a default logger instance with simplified configuration
const defaultLogger = new Logger()

// Interface to match the middleware logger
interface LogOptions {
  context?: string
  data?: unknown
}

// Create a compatibility layer with performance optimization
export const createLogger = (appName = "Roulette") => {
  // Create a logger with the app name in the context
  const baseLogger = defaultLogger.forSource(appName)

  // Wrap with performant logger to prevent re-renders
  const performantLogger = new PerformantLogger(baseLogger)

  return {
    debug: (message: string, options?: LogOptions) => {
      performantLogger.debug(message, {
        component: options?.context,
        ...(options?.data as object),
      })
    },

    info: (message: string, options?: LogOptions) => {
      performantLogger.info(message, {
        component: options?.context,
        ...(options?.data as object),
      })
    },

    warn: (message: string, options?: LogOptions) => {
      performantLogger.warn(message, {
        component: options?.context,
        ...(options?.data as object),
      })
    },

    error: (message: string, error?: unknown, options?: LogOptions) => {
      performantLogger.error(message, {
        component: options?.context,
        error,
        ...(options?.data as object),
      })
    },

    logError: (
      error: Error,
      additionalMessage?: string,
      options?: LogOptions
    ) => {
      performantLogger.logError(error, additionalMessage, {
        component: options?.context,
        ...(options?.data as object),
      })
    },
  }
}

/**
 * Utility function to clear log storage
 */
export const clearLogStorage = async (): Promise<boolean> => {
  try {
    await localforage.removeItem("app_logs")
    return true
  } catch (error) {
    return false
  }
}

// Create a default logger instance for compatibility
export const logger = createLogger()
