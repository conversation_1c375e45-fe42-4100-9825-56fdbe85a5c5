import { motion } from "motion/react"
import { useRef, useState, useEffect, useMemo, useCallback } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useMobile } from "@/hooks/use-mobile"
import { useReducedMotion } from "@/hooks/use-reduced-motion"
import { useResponsiveSize } from "@/hooks/use-responsive-size"
import { useTouchGestures } from "@/hooks/use-touch-gestures"
import { cn } from "@/lib/utils"
import { imageCache } from "@/lib/image-cache"
import { logger } from "@/middleware"
import { ControlButton } from "./components/control-button"
import { ControlButtonSkeleton } from "./components/control-button-skeleton"
import {
  useControlPanelTheme,
  type ControlPanelTheme,
  themeStyles,
} from "./components/theme-context"

export interface ControlPanelButton {
  id: string
  src: string
  alt: string
  onClick?: () => void
  disabled?: boolean
  active?: boolean
  value?: number | string
  isChip?: boolean
  aspect?: "square" | "freeform"
}

export interface ControlPanelProps {
  buttons: ControlPanelButton[]
  className?: string
  buttonSize?: "sm" | "md" | "lg"
  borderColor?: string
  backgroundColor?: string
  spacing?: number
  showDividers?: boolean
  showTooltips?: boolean
  theme?: ControlPanelTheme
  autoResponsive?: boolean
  orientation?: "horizontal" | "vertical"
  progressiveLoading?: boolean
  ariaLabel?: string
  zeroGapForChips?: boolean
}

export function ControlPanel({
  buttons,
  className,
  buttonSize: propButtonSize,
  borderColor: propBorderColor,
  backgroundColor: propBackgroundColor,
  spacing = 14,
  showDividers = false,
  showTooltips = false,
  theme: propTheme,
  autoResponsive = true,
  orientation = "horizontal",
  progressiveLoading = true,
  ariaLabel = "Control panel",
  zeroGapForChips = false,
}: ControlPanelProps) {
  const scrollRef = useRef<HTMLDivElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [loadedButtons, setLoadedButtons] = useState<Record<string, boolean>>(
    {}
  )
  const [dividerLoaded, setDividerLoaded] = useState(false)
  const [forceShowButtons, setForceShowButtons] = useState(false)
  const loadTimeoutRef = useRef<number | null>(null)

  const checkScrollPosition = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      )
      if (!viewport) return

      const viewportElement = viewport as HTMLElement

      if (orientation === "vertical") {
        const { scrollTop, scrollHeight, clientHeight } = viewportElement

        logger.debug("Vertical scroll position check", {
          context: "ControlPanel",
          data: {
            scrollTop,
            scrollHeight,
            clientHeight,
            canScrollTop: scrollTop > 0,
            canScrollBottom: scrollTop + clientHeight < scrollHeight,
          },
        })

        // Update scroll indicators for vertical orientation
        const container = scrollRef.current?.parentElement
        if (container) {
          container.classList.toggle("can-scroll-top", scrollTop > 0)
          container.classList.toggle(
            "can-scroll-bottom",
            scrollTop + clientHeight < scrollHeight
          )
        }
      } else {
        const { scrollLeft, scrollWidth, clientWidth } = viewportElement

        logger.debug("Horizontal scroll position check", {
          context: "ControlPanel",
          data: {
            scrollLeft,
            scrollWidth,
            clientWidth,
            canScroll: scrollWidth > clientWidth,
            canScrollLeft: scrollLeft > 0,
            canScrollRight: scrollLeft + clientWidth < scrollWidth,
          },
        })

        // Update scroll indicators for horizontal orientation
        const container = scrollRef.current?.parentElement
        if (container) {
          container.classList.toggle("can-scroll-left", scrollLeft > 0)
          container.classList.toggle(
            "can-scroll-right",
            scrollLeft + clientWidth < scrollWidth
          )
        }
      }
    }
  }, [orientation])

  const themeContext = useControlPanelTheme()

  const themeStyle = propTheme ? themeStyles[propTheme] : themeContext.styles

  const responsiveSize = useResponsiveSize(propButtonSize || "lg")
  const buttonSize = autoResponsive ? responsiveSize : propButtonSize || "lg"

  const prefersReducedMotion = useReducedMotion()

  const { ref: touchRef } = useTouchGestures<HTMLDivElement>({
    onSwipeLeft: () =>
      orientation === "horizontal" ? scrollContent(300) : undefined,
    onSwipeRight: () =>
      orientation === "horizontal" ? scrollContent(-300) : undefined,
    onSwipeUp: () =>
      orientation === "vertical" ? scrollContent(300) : undefined,
    onSwipeDown: () =>
      orientation === "vertical" ? scrollContent(-300) : undefined,
  })

  const setRefs = (element: HTMLDivElement | null) => {
    scrollRef.current = element
    if (touchRef.current !== null) {
      touchRef.current = element
    }
  }

  const scrollContent = useCallback(
    (amount: number) => {
      if (scrollAreaRef.current) {
        const viewport = scrollAreaRef.current.querySelector(
          "[data-radix-scroll-area-viewport]"
        )
        if (!viewport) return

        const viewportElement = viewport as HTMLElement

        if (orientation === "vertical") {
          viewportElement.scrollBy({
            top: amount,
            behavior: prefersReducedMotion ? "auto" : "smooth",
          })
        } else {
          viewportElement.scrollBy({
            left: amount,
            behavior: prefersReducedMotion ? "auto" : "smooth",
          })
        }

        setTimeout(checkScrollPosition, 300)
      }
    },
    [prefersReducedMotion, checkScrollPosition, orientation]
  )

  useEffect(() => {
    const initialLoadState: Record<string, boolean> = {}
    const imagesToPreload: string[] = []

    buttons.forEach((button) => {
      if (button && button.id) {
        // Check if image is already cached
        const isLoaded = imageCache.isLoaded(button.src)
        initialLoadState[button.id] = isLoaded

        if (!isLoaded) {
          imagesToPreload.push(button.src)
        }

        logger.debug(`Initial button load state: ${button.id} = ${isLoaded}`, {
          context: "ControlPanel",
          data: { src: button.src },
        })
      }
    })

    setLoadedButtons(initialLoadState)

    // Preload images using cache to prevent redundant requests
    if (imagesToPreload.length > 0) {
      imageCache
        .preloadMultiple(imagesToPreload)
        .then(() => {
          // Update loaded state for all buttons
          const updatedLoadState: Record<string, boolean> = {}
          buttons.forEach((button) => {
            if (button && button.id) {
              updatedLoadState[button.id] = true
            }
          })
          setLoadedButtons(updatedLoadState)
        })
        .catch((error) => {
          logger.warn("Failed to preload some button images", {
            context: "ControlPanel",
            data: { error: error.message },
          })
          // Still mark as loaded to prevent infinite loading
          setForceShowButtons(true)
        })
    }

    setIsVisible(true)
    checkScrollPosition()

    if (loadTimeoutRef.current) {
      window.clearTimeout(loadTimeoutRef.current)
    }

    loadTimeoutRef.current = window.setTimeout(() => {
      logger.warn("Forcing button display after timeout", {
        context: "ControlPanel",
      })
      setForceShowButtons(true)
      loadTimeoutRef.current = null
    }, 5000) as unknown as number

    return () => {
      if (loadTimeoutRef.current) {
        window.clearTimeout(loadTimeoutRef.current)
        loadTimeoutRef.current = null
      }
    }
  }, [checkScrollPosition, buttons])

  useEffect(() => {
    if (!showDividers) {
      setDividerLoaded(true)
      return
    }

    const dividerImg = new Image()

    const handleLoad = () => setDividerLoaded(true)
    const handleError = () => {
      logger.warn("Failed to load divider image", { context: "ControlPanel" })
      setDividerLoaded(true)
    }

    dividerImg.addEventListener("load", handleLoad)
    dividerImg.addEventListener("error", handleError)
    dividerImg.src = "/images/divider.png"

    return () => {
      dividerImg.removeEventListener("load", handleLoad)
      dividerImg.removeEventListener("error", handleError)
    }
  }, [showDividers])

  useEffect(() => {
    const scrollArea = scrollAreaRef.current
    if (!scrollArea) return

    const viewport = scrollArea.querySelector(
      "[data-radix-scroll-area-viewport]"
    )
    if (!viewport) return

    const debouncedCheckScroll = () => {
      if (scrollAreaRef.current) {
        requestAnimationFrame(checkScrollPosition)
      }
    }

    const handleWheel = (e: Event) => {
      const wheelEvent = e as WheelEvent

      // For horizontal orientation, we want to scroll horizontally when user scrolls vertically
      if (orientation === "horizontal") {
        if (
          wheelEvent.shiftKey ||
          Math.abs(wheelEvent.deltaX) > Math.abs(wheelEvent.deltaY)
        )
          return

        if (Math.abs(wheelEvent.deltaY) > 0) {
          e.preventDefault()
          scrollContent(wheelEvent.deltaY * 2)
          debouncedCheckScroll()
        }
      }
      // For vertical orientation, let the native vertical scrolling work
    }

    viewport.addEventListener("scroll", debouncedCheckScroll, { passive: true })

    // Only add wheel event handler for horizontal orientation
    if (orientation === "horizontal") {
      viewport.addEventListener("wheel", handleWheel, { passive: false })
    }

    window.addEventListener("resize", debouncedCheckScroll)

    checkScrollPosition()

    return () => {
      viewport.removeEventListener("scroll", debouncedCheckScroll)
      if (orientation === "horizontal") {
        viewport.removeEventListener("wheel", handleWheel)
      }
      window.removeEventListener("resize", debouncedCheckScroll)
    }
  }, [checkScrollPosition, scrollContent, orientation])

  useEffect(() => {
    const newLoadState = { ...loadedButtons }
    let hasChanges = false
    const imagesToPreload: string[] = []

    buttons.forEach((button) => {
      if (button && button.id && !loadedButtons[button.id]) {
        // Check if image is already cached
        if (imageCache.isLoaded(button.src)) {
          newLoadState[button.id] = true
          hasChanges = true
          logger.debug(`Button image already in cache: ${button.id}`, {
            context: "ControlPanel",
          })
        } else {
          imagesToPreload.push(button.src)
        }
      }
    })

    // Preload any missing images using cache
    if (imagesToPreload.length > 0) {
      imagesToPreload.forEach((src) => {
        imageCache
          .preload(src)
          .then(() => {
            const button = buttons.find((b) => b.src === src)
            if (button) {
              logger.debug(`Button image loaded: ${button.id}`, {
                context: "ControlPanel",
              })
              setLoadedButtons((prev) => ({
                ...prev,
                [button.id]: true,
              }))
            }
          })
          .catch(() => {
            const button = buttons.find((b) => b.src === src)
            if (button) {
              logger.warn(`Button image load error: ${button.id}`, {
                context: "ControlPanel",
                data: { src: button.src },
              })
              setLoadedButtons((prev) => ({
                ...prev,
                [button.id]: true,
              }))
            }
          })
      })
    }

    if (hasChanges) {
      setLoadedButtons(newLoadState)
    }
  }, [buttons, loadedButtons])

  const allImagesLoaded = useMemo(
    () =>
      buttons.every(
        (button) => button && button.id && loadedButtons[button.id]
      ) &&
      (!showDividers || dividerLoaded),
    [buttons, loadedButtons, showDividers, dividerLoaded]
  )

  const allButtonsAreChips = useMemo(
    () =>
      buttons.length > 0 && buttons.every((button) => button && button.isChip),
    [buttons]
  )

  const finalSpacing = useMemo(
    () => (allButtonsAreChips && zeroGapForChips ? 0 : spacing),
    [allButtonsAreChips, zeroGapForChips, spacing]
  )

  const finalBorderColor = propBorderColor || themeStyle.borderColor
  const finalBackgroundColor = propBackgroundColor || themeStyle.backgroundColor
  const finalActiveColor = themeStyle.activeColor
  const finalHoverColor = themeStyle.hoverColor

  const animationConfig = prefersReducedMotion
    ? { stiffness: 200, damping: 30 }
    : { stiffness: 100, damping: 20 }

  const isMobile = useMobile()

  return (
    <motion.div
      className={cn(
        "relative rounded-lg",
        orientation === "vertical" ? "flex flex-col" : "",
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
      transition={{
        opacity: {
          type: "spring",
          stiffness: animationConfig.stiffness,
          damping: animationConfig.damping,
        },
        y: {
          type: "spring",
          stiffness: animationConfig.stiffness,
          damping: animationConfig.damping,
        },
      }}
      style={{ backgroundColor: "transparent" }}
      ref={setRefs}
    >
      <div
        className={cn(
          "lg:pt-2 lg:px-2 lg:pb-1 rounded-lg relative flex items-center justify-center",
          orientation === "vertical" ? "flex-col" : "",
          orientation === "vertical"
            ? "control-panel-scroll-container vertical"
            : "control-panel-scroll-container"
        )}
        style={{
          border: `2px solid ${finalBorderColor}`,
          backgroundColor: finalBackgroundColor,
        }}
      >
        {/* Scroll buttons removed */}

        <ScrollArea
          className={cn(
            "w-full",
            orientation === "vertical"
              ? "h-full overflow-y-auto control-panel-scroll-area vertical"
              : "overflow-x-auto control-panel-scroll-area",
            "flex items-center justify-center"
          )}
          type='scroll'
          ref={scrollAreaRef}
          onScroll={checkScrollPosition}
          scrollHideDelay={0}
        >
          <div
            className={cn(
              orientation === "vertical"
                ? "flex flex-col items-center justify-center"
                : "flex items-center justify-center",
              "min-w-max mx-auto"
            )}
            style={{
              gap: isMobile ? "2px" : `${finalSpacing}px`,
              paddingLeft: isMobile ? "2px" : "6px",
              paddingRight: isMobile ? "2px" : "6px",
            }}
            role='toolbar'
            aria-label={ariaLabel}
          >
            {buttons.map((button, index) => {
              if (!button || !button.id) return null

              const isLoaded =
                forceShowButtons ||
                (progressiveLoading
                  ? loadedButtons[button.id]
                  : allImagesLoaded)

              return (
                <div
                  key={button.id}
                  className={cn(
                    "flex items-center justify-center",
                    orientation === "vertical" ? "flex-col" : "",

                    buttonSize === "sm"
                      ? "h-[56px]"
                      : buttonSize === "md"
                      ? "h-[72px]"
                      : "h-[88px]"
                  )}
                >
                  {!isLoaded ? (
                    <ControlButtonSkeleton size={buttonSize} index={index} />
                  ) : (
                    <ControlButton
                      id={button.id}
                      src={button.src}
                      alt={button.alt}
                      onClick={button.onClick}
                      disabled={button.disabled}
                      active={button.active}
                      size={buttonSize}
                      showTooltip={showTooltips}
                      activeColor={finalActiveColor}
                      hoverColor={finalHoverColor}
                      value={button.value}
                      isChip={button.isChip}
                      aspect={button.aspect}
                    />
                  )}
                  {showDividers && index < buttons.length - 1 && (
                    <div
                      className={cn(
                        orientation === "vertical"
                          ? "my-1 w-full flex justify-center"
                          : "mx-1 h-full flex items-center"
                      )}
                    >
                      {!dividerLoaded ? (
                        <div
                          className={cn(
                            orientation === "vertical"
                              ? "h-[2px] w-8 bg-gray-700 opacity-30 select-none"
                              : "h-8 w-[2px] bg-gray-700 opacity-30 select-none"
                          )}
                        />
                      ) : (
                        <img
                          src='/images/divider.png'
                          alt=''
                          width={orientation === "vertical" ? 40 : 2}
                          height={orientation === "vertical" ? 2 : 40}
                          className={cn(
                            orientation === "vertical"
                              ? "w-8 h-[2px]"
                              : "h-8 w-[2px]",
                            "select-none"
                          )}
                          draggable='false'
                        />
                      )}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </ScrollArea>

        {/* Scroll buttons removed */}
      </div>
    </motion.div>
  )
}
