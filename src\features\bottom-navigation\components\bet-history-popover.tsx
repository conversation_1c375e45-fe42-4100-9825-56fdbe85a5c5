import React, { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  SVGIcon,
  PlayerBetHistory,
  useRoundPhase,
  usePlayerBetHistoryQuery,
} from "@/hooks"
import { formatCurrency } from "@/lib/formatting"
import { logger, findLiteralChip } from "@/middleware"
import { useTokenData } from "@/stores/auth-store"
import { useBettingStore } from "@/stores/betting-store"
import {
  blackCell,
  evenCell,
  firstTwelveCell,
  nineteenToThirtySix,
  oddCell,
  oneToEighteen,
  redCell,
  rouletteSelectors,
  secondTwelveCell,
  SelectableCell,
  thirdTwelveCell,
  twoToOneCells,
} from "@/config/selector-config"
import { withErrorBoundary } from "./with-error-boundary"

const specialBetMap = {
  "1st 12": firstTwelveCell,
  "2nd 12": secondTwelveCell,
  "3rd 12": thirdTwelveCell,
  "2:1-1": twoToOneCells[0],
  "2:1-2": twoToOneCells[1],
  "2:1-3": twoToOneCells[2],
  "1-18": oneToEighteen,
  "19-36": nineteenToThirtySix,
  Black: blackCell,
  Red: redCell,
  Odd: oddCell,
  Even: evenCell,
}

// Inner component that will be wrapped with error boundary
const BetHistoryPopoverInner: React.FC = () => {
  const addChip = useBettingStore((state) => state.addChip)
  const tokenData = useTokenData()
  const { Spinning } = useRoundPhase()
  const [popoverOpen, setPopoverOpen] = useState(false)

  // Use React Query for player bet history
  const { data: playerHistory = [] } = usePlayerBetHistoryQuery(
    tokenData?.PlayerID || "",
    !!tokenData && popoverOpen
  )

  // Log when history is loaded
  useEffect(() => {
    if (playerHistory.length > 0) {
      logger.debug("Player bet history loaded", {
        context: "BetHistoryPopover",
        data: { count: playerHistory.length },
      })
    }
  }, [playerHistory])

  const loadRoundBets = (bet: PlayerBetHistory) => {
    const chip = findLiteralChip(bet.stake)
    if (!chip) return
    const specialCell = specialBetMap[bet.cellId as keyof typeof specialBetMap]
    if (specialCell) {
      addChip(specialCell, chip)
    } else {
      const cell = rouletteSelectors.find(
        (selector) => selector.cell_id.toString() === bet.cellId
      )
      if (cell) {
        const historicalBet: SelectableCell = { ...cell, chips_placed: [] }
        addChip(historicalBet, chip)
      }
    }
    setPopoverOpen(false)
  }

  useEffect(() => {
    if (Spinning) setPopoverOpen(false)
  }, [Spinning])

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger disabled={Spinning} asChild>
        <Button className='bg-transparent p-0 transition-all duration-300 ease-in-out hover:scale-[0.95]'>
          <SVGIcon
            url='/assets/svgs/receipt-text.svg'
            className='h-auto min-w-12 border-2 p-2 rounded-full transition-all duration-300 ease-in-out hover:scale-[0.95]'
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-96 p-0'>
        <Card className='bg-transparent'>
          <CardHeader className='pb-3'>
            <CardTitle>Round History</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className='h-[400px] w-full pr-4'>
              {Object.entries(playerHistory).length > 0
                ? Object.entries(playerHistory)
                    .sort(([a], [b]) => Number(b) - Number(a))
                    .map(([roundId, bet]) => (
                      <div
                        key={`round-${roundId}`}
                        className='mb-4 cursor-pointer rounded-lg border border-gray-700 p-3 transition-all hover:bg-gray-800/50'
                        onClick={() => loadRoundBets(bet)}
                      >
                        <div className='flex flex-col space-y-2'>
                          <div className='flex justify-between border-b border-gray-700 pb-2'>
                            <span className='text-sm font-medium'>
                              Round #{roundId}
                            </span>
                            <span className='text-sm font-medium'>
                              Total Stake: {formatCurrency(bet.stake)}
                            </span>
                          </div>
                          <div className='space-y-2'>
                            <div className='flex flex-col text-sm'>
                              <div className='flex justify-between'>
                                <span>
                                  {bet.betTypeName}: {formatCurrency(bet.stake)}
                                </span>
                                <span className='text-green-400'>
                                  Potential:{" "}
                                  {formatCurrency(bet.potentialPayout)}
                                </span>
                              </div>
                              <span className='text-gray-400'>
                                on {bet.betNumbers}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                : Array.from({ length: 10 }).map(() => (
                    <div className='flex h-full w-full animate-pulse bg-neutral-800 items-center justify-center'></div>
                  ))}
            </ScrollArea>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
}

// Use the HOC to wrap the component with an error boundary

// Export the wrapped component
export const BetHistoryPopover = withErrorBoundary(
  BetHistoryPopoverInner,
  "BetHistoryPopover"
)
