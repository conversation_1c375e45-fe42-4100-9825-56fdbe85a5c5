#!/usr/bin/env node

/**
 * Code Reference Updater
 * Automatically finds and updates image file references in the codebase
 */

import fs from 'fs/promises'
import path from 'path'
import { glob } from 'glob'

export class CodeReferenceUpdater {
  constructor(config = {}) {
    this.config = {
      fileExtensions: ['.ts', '.tsx', '.js', '.jsx', '.css', '.scss', '.sass'],
      excludeFiles: [
        'node_modules/**',
        'dist/**',
        'build/**',
        '*.min.js',
        '*.min.css',
        'image-optimization-backups/**'
      ],
      dryRun: false,
      verbose: false,
      ...config
    }
    
    this.updates = []
    this.errors = []
  }

  /**
   * Find all code files that might contain image references
   */
  async findCodeFiles() {
    const patterns = this.config.fileExtensions.map(ext => `**/*${ext}`)
    const allFiles = []

    for (const pattern of patterns) {
      try {
        const files = await glob(pattern, {
          ignore: this.config.excludeFiles,
          absolute: true
        })
        allFiles.push(...files)
      } catch (error) {
        this.errors.push(`Failed to find files with pattern ${pattern}: ${error.message}`)
      }
    }

    return [...new Set(allFiles)] // Remove duplicates
  }

  /**
   * Create regex patterns for finding image references
   */
  createImageReferencePatterns(originalPath, newPath) {
    const originalName = path.basename(originalPath)
    const newName = path.basename(newPath)
    const originalExt = path.extname(originalPath)
    const newExt = path.extname(newPath)
    
    // Handle different types of references
    const patterns = []
    
    // 1. Direct string references (imports, src attributes, etc.)
    patterns.push({
      pattern: new RegExp(`(['"\`])([^'"\`]*${this.escapeRegex(originalName)})\\1`, 'g'),
      replacement: (match, quote, fullPath) => {
        const updatedPath = fullPath.replace(originalName, newName)
        return `${quote}${updatedPath}${quote}`
      },
      description: 'Direct string references'
    })

    // 2. Import statements
    patterns.push({
      pattern: new RegExp(`(import\\s+[^'"\`]*from\\s+['"\`])([^'"\`]*${this.escapeRegex(originalName)})(['"\`])`, 'g'),
      replacement: (match, prefix, fullPath, quote) => {
        const updatedPath = fullPath.replace(originalName, newName)
        return `${prefix}${updatedPath}${quote}`
      },
      description: 'Import statements'
    })

    // 3. CSS url() references
    patterns.push({
      pattern: new RegExp(`(url\\s*\\(\\s*['"\`]?)([^'"\`\\)]*${this.escapeRegex(originalName)})(['"\`]?\\s*\\))`, 'g'),
      replacement: (match, prefix, fullPath, suffix) => {
        const updatedPath = fullPath.replace(originalName, newName)
        return `${prefix}${updatedPath}${suffix}`
      },
      description: 'CSS url() references'
    })

    // 4. JSX src attributes
    patterns.push({
      pattern: new RegExp(`(src\\s*=\\s*['"\`])([^'"\`]*${this.escapeRegex(originalName)})(['"\`])`, 'g'),
      replacement: (match, prefix, fullPath, quote) => {
        const updatedPath = fullPath.replace(originalName, newName)
        return `${prefix}${updatedPath}${quote}`
      },
      description: 'JSX src attributes'
    })

    return patterns
  }

  /**
   * Escape special regex characters
   */
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  /**
   * Update references in a single file
   */
  async updateFileReferences(filePath, imageMapping) {
    try {
      const content = await fs.readFile(filePath, 'utf8')
      let updatedContent = content
      let fileUpdates = []

      for (const [originalPath, newPath] of Object.entries(imageMapping)) {
        const patterns = this.createImageReferencePatterns(originalPath, newPath)
        
        for (const { pattern, replacement, description } of patterns) {
          const matches = [...content.matchAll(pattern)]
          
          if (matches.length > 0) {
            updatedContent = updatedContent.replace(pattern, replacement)
            
            fileUpdates.push({
              originalPath,
              newPath,
              patternType: description,
              matchCount: matches.length,
              matches: matches.map(match => ({
                text: match[0],
                index: match.index
              }))
            })

            if (this.config.verbose) {
              console.log(`  📝 Updated ${matches.length} ${description} references: ${originalPath} → ${newPath}`)
            }
          }
        }
      }

      // Write updated content if changes were made and not in dry run mode
      if (updatedContent !== content) {
        if (!this.config.dryRun) {
          await fs.writeFile(filePath, updatedContent, 'utf8')
        }

        this.updates.push({
          filePath,
          updates: fileUpdates,
          dryRun: this.config.dryRun
        })

        return true
      }

      return false
    } catch (error) {
      this.errors.push(`Failed to update file ${filePath}: ${error.message}`)
      return false
    }
  }

  /**
   * Update all code references for the given image mapping
   */
  async updateAllReferences(imageMapping) {
    console.log('🔍 Finding code files...')
    const codeFiles = await this.findCodeFiles()
    console.log(`📁 Found ${codeFiles.length} code files to scan`)

    if (this.config.dryRun) {
      console.log('🧪 Running in DRY RUN mode - no files will be modified')
    }

    let updatedFiles = 0
    let totalUpdates = 0

    for (const filePath of codeFiles) {
      const relativePath = path.relative(process.cwd(), filePath)
      
      if (this.config.verbose) {
        console.log(`🔍 Scanning: ${relativePath}`)
      }

      const wasUpdated = await this.updateFileReferences(filePath, imageMapping)
      
      if (wasUpdated) {
        updatedFiles++
        const fileUpdateCount = this.updates[this.updates.length - 1].updates.reduce(
          (sum, update) => sum + update.matchCount, 0
        )
        totalUpdates += fileUpdateCount
        console.log(`✅ Updated: ${relativePath} (${fileUpdateCount} references)`)
      }
    }

    return {
      scannedFiles: codeFiles.length,
      updatedFiles,
      totalUpdates,
      updates: this.updates,
      errors: this.errors
    }
  }

  /**
   * Generate a detailed report of all updates
   */
  generateReport() {
    const report = {
      summary: {
        scannedFiles: 0,
        updatedFiles: this.updates.length,
        totalUpdates: this.updates.reduce((sum, file) => 
          sum + file.updates.reduce((fileSum, update) => fileSum + update.matchCount, 0), 0
        ),
        errors: this.errors.length
      },
      updates: this.updates,
      errors: this.errors,
      timestamp: new Date().toISOString()
    }

    return report
  }
}

// Export for use in other modules
export default CodeReferenceUpdater

// CLI usage when run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2)
  
  if (args.length < 2) {
    console.log('Usage: node update-references.js <original-path> <new-path> [--dry-run] [--verbose]')
    console.log('Example: node update-references.js image.png image.webp --dry-run')
    process.exit(1)
  }

  const [originalPath, newPath] = args
  const dryRun = args.includes('--dry-run')
  const verbose = args.includes('--verbose')

  const updater = new CodeReferenceUpdater({ dryRun, verbose })
  const imageMapping = { [originalPath]: newPath }

  updater.updateAllReferences(imageMapping)
    .then(result => {
      console.log('\n📊 Update Summary:')
      console.log(`   Scanned files: ${result.scannedFiles}`)
      console.log(`   Updated files: ${result.updatedFiles}`)
      console.log(`   Total updates: ${result.totalUpdates}`)
      
      if (result.errors.length > 0) {
        console.log(`   Errors: ${result.errors.length}`)
        result.errors.forEach(error => console.log(`     ❌ ${error}`))
      }

      if (dryRun) {
        console.log('\n🧪 This was a dry run - no files were actually modified')
      }
    })
    .catch(error => {
      console.error('❌ Failed to update references:', error.message)
      process.exit(1)
    })
}
