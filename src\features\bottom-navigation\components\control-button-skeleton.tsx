"use client"

import { motion } from "motion/react"
import { useReducedMotion } from "@/hooks/use-reduced-motion"
import { cn } from "@/lib/utils"

interface ControlButtonSkeletonProps {
  size?: "sm" | "md" | "lg"
  className?: string
  index?: number // For staggered animation
}

export function ControlButtonSkeleton({ size = "md", className, index = 0 }: ControlButtonSkeletonProps) {
  const prefersReducedMotion = useReducedMotion()

  // Size mapping
  const sizeMap = {
    sm: 40,
    md: 56,
    lg: 72,
  }

  const buttonSize = sizeMap[size] || sizeMap.md

  // Staggered animation delay based on index
  const animationDelay = index * 100

  // If reduced motion is preferred, render a static skeleton
  if (prefersReducedMotion) {
    return (
      <div
        className={cn("rounded-lg overflow-hidden bg-gradient-to-r from-gray-700 to-gray-800", className)}
        style={{
          width: buttonSize,
          height: buttonSize,
          opacity: 0.5,
        }}
        aria-hidden="true"
      />
    )
  }

  // Use a custom variant to handle the pulsing effect
  return (
    <motion.div
      className={cn("rounded-lg overflow-hidden bg-gradient-to-r from-gray-700 to-gray-800", className)}
      initial={{ opacity: 0 }}
      animate={{
        opacity: [0.3, 0.6, 0.3],
      }}
      transition={{
        opacity: {
          duration: 1.5,
          repeat: Infinity,
          delay: animationDelay / 1000, // Convert ms to seconds
          ease: "easeInOut"
        }
      }}
      style={{
        width: buttonSize,
        height: buttonSize,
      }}
      aria-hidden="true"
    />
  )
}
