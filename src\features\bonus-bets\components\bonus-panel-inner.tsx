import { motion, AnimatePresence } from "motion/react"
import type React from "react"
import { useEffect, useState } from "react"
import { useRoundPhase } from "@/hooks"
import { useMobile } from "@/hooks/use-mobile"
import { bonusSelectors } from "@/config/bonus-config"
import {
  getBetButtons,
  BONUS_PANEL_STYLES,
} from "../constants/bonus-panel-styles"
import { useDragonAnimation } from "../hooks/use-dragon-animation"
import { useDragonPreloader } from "../hooks/use-dragon-preloader"
import { useSymbolSizing } from "../hooks/use-symbol-sizing"
import { BonusSymbolsGrid } from "./bonus-symbols-grid"
import { DragonLayout } from "./dragon-layout"
import { MobileBonusSymbols } from "./mobile-bonus-symbols"

/**
 * Inner component containing the core logic for the Bonus Bets Panel
 */
export const BonusPanelInner: React.FC = () => {
  const isMobile = useMobile()
  const { Betting, Spinning } = useRoundPhase()
  const [showDragon, setShowDragon] = useState(!Betting)

  const betButtons = getBetButtons(bonusSelectors)

  // Custom hooks for functionality
  useDragonPreloader()
  const { spritesheetRef } = useDragonAnimation(Spinning, {
    totalFrames: 10,
    frameHeight: 128,
    targetBonusNumber: 0,
    settleAnimationDuration: 2000,
  })
  const { containerRef } = useSymbolSizing({
    isMobile,
    showDragon,
    betButtonsLength: betButtons.length,
  })

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowDragon(!Betting)
    }, 400)

    return () => clearTimeout(timer)
  }, [Betting])

  return (
    <div ref={containerRef} className={BONUS_PANEL_STYLES.mainContainer}>
      <AnimatePresence mode='wait'>
        {!isMobile ? (
          <>
            <motion.div
              key='dragon-layout'
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.5,
                type: "spring",
                stiffness: 100,
                damping: 15,
              }}
              className='w-full h-full'
            >
              <DragonLayout
                isMobile={isMobile}
                spritesheetRef={spritesheetRef}
              />
            </motion.div>
            <motion.div
              key='bonus-symbols-grid'
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{
                duration: 0.5,
                type: "spring",
                stiffness: 100,
                damping: 15,
              }}
              className='w-full h-auto flex items-end justify-center pb-2'
            >
              <BonusSymbolsGrid isMobile={isMobile} showDragon={showDragon} />
            </motion.div>
          </>
        ) : (
          <>
            {showDragon ? (
              <>
                <motion.div
                  key='mobile-dragon'
                  initial={{ opacity: 0, scale: 0.95, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -20 }}
                  transition={{
                    duration: 0.5,
                    type: "spring",
                    stiffness: 100,
                    damping: 15,
                  }}
                  className='w-full h-full'
                >
                  <DragonLayout
                    isMobile={isMobile}
                    spritesheetRef={spritesheetRef}
                  />
                </motion.div>
              </>
            ) : (
              <motion.div
                key='mobile-bonus-symbols'
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                transition={{
                  duration: 0.5,
                  type: "spring",
                  stiffness: 100,
                  damping: 15,
                }}
                className='w-full h-full flex flex-col py-2'
              >
                <MobileBonusSymbols betButtons={betButtons} />
              </motion.div>
            )}
          </>
        )}
      </AnimatePresence>
    </div>
  )
}
