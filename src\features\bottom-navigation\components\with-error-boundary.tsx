import React from 'react'
import { EnhancedErrorBoundary } from '@/middleware'

/**
 * Higher-order component that wraps a component with an error boundary
 * 
 * @param Component - Component to wrap
 * @param displayName - Display name for the component (used for error context)
 * @returns Wrapped component with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  displayName: string
): React.FC<P> {
  // Create a wrapper component
  const WrappedComponent: React.FC<P> = (props) => {
    return (
      <EnhancedErrorBoundary context={displayName}>
        <Component {...props} />
      </EnhancedErrorBoundary>
    )
  }

  // Set display name for debugging
  WrappedComponent.displayName = `withErrorBoundary(${displayName})`

  return WrappedComponent
}
