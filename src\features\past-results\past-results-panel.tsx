import { ChevronDown } from "lucide-react"
import { useEffect } from "react"
import Divider from "@/components/ui/Divider"
import { ScrollFade } from "@/components/ui/scroll-fade"
import { useHistoryQuery } from "@/hooks"
import { cn } from "@/lib/utils"
import { useGameStateStore } from "@/stores/game-state-store"
import { bonusSelectors } from "@/config/bonus-config"
import GameTimer from "./game-timer"

const PastResultsPanel = () => {
  const { data: history = [] } = useHistoryQuery(250)

  return (
    <section className='flex flex-col h-full text-white overflow-hidden'>
      <div className='py-2 px-2 h-full flex flex-col'>
        <h3 className='text-2xl font-bold text-center text-[#C69E61]'>
          Past Results
        </h3>
        <Divider />
        <ScrollFade
          direction='y-both'
          size='sm'
          className='h-0 flex-grow mt-2 mb-2'
        >
          <div className={cn("grid gap-2 p-2 grid-cols-6")}>
            {history?.slice(0, 150).map((history, index) => (
              <div
                key={`${history.rouletteNumber}-${history.bonusNumber}-${index}`}
                className='flex h-full w-auto flex-col font-semibold items-center justify-center border border-[#C69E61] bg-[#262626] rounded-lg'
              >
                <span
                  className={cn(
                    "text-xl lg:text-2xl leading-tight",
                    [
                      1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32,
                      34, 36,
                    ].includes(history.rouletteNumber)
                      ? "text-[#d00000]"
                      : ""
                  )}
                >
                  {history.rouletteNumber}
                </span>
                <img
                  className='aspect-square max-h-8 max-w-8'
                  src={bonusSelectors[history.bonusNumber - 1]?.bonusSource}
                  alt={history.bonusNumber.toString()}
                />
              </div>
            ))}
          </div>
        </ScrollFade>
        <Divider />
        <ChevronDown className='mx-auto -mb-2 h-6 w-6 duration-1000 ease-linear animate-bounce' />
      </div>

      {/* Game Timer */}
      <GameTimer initialTime={30} showLabel={true} />
    </section>
  )
}

export default PastResultsPanel
