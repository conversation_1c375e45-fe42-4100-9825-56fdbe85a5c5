import React from "react"
import { ChipStack } from "@/components/common/chip-stack"
import { cn } from "@/lib/utils"
import {
  SelectableCell,
  rouletteSelectors,
  twoToOneCells,
  firstTwelveCell,
  secondTwelveCell,
  thirdTwelveCell,
  oneToEighteen,
  evenCell,
  blackCell,
  redCell,
  oddCell,
  nineteenToThirtySix,
} from "@/config/selector-config"

export interface FunctionLayerProps {
  handleMouseEnter: (cell: SelectableCell) => void
  handleCellClick: (cell: SelectableCell) => void
}

export const FunctionLayer: React.FC<FunctionLayerProps> = React.memo(
  ({ handleMouseEnter, handleCellClick }) => (
    <main className='absolute left-0 top-0 z-10 grid h-full w-full translate-x-0 translate-y-0 lg:grid-cols-12 lg:grid-rows-5 grid-rows-12 grid-cols-5'>
      {/* Zero cell */}
      <button
        onMouseEnter={() =>
          handleMouseEnter({
            cell_id: 0,
            chips_placed: [],
            cell_selectors: ["0"],
            isSelectable: true,
          })
        }
        onClick={() =>
          handleCellClick({
            cell_id: 0,
            chips_placed: [],
            cell_selectors: ["0"],
            isSelectable: true,
          })
        }
        className='relative   lg:col-start-1 lg:row-start-2 lg:col-span-1 lg:row-end-5 col-start-2 row-start-1 col-span-3 flex h-full w-full items-center justify-center py-[5px]'
      >
        <ChipStack cellId={0} />
      </button>

      {/* Main grid of selectors */}
      <section className='absolute lg:h-[122%] lg:w-[104%] h-[105%] w-[130%] lg:-top-8 lg:-left-6 -top-3 -left-7 z-20 grid lg:col-end-12 lg:grid-cols-[repeat(25,minmax(0,1fr))] lg:grid-rows-7 lg:row-end-5 col-start-2 row-start-2 row-end-12 col-span-3 grid-rows-[repeat(25,minmax(0,1fr))] grid-cols-7 '>
        {rouletteSelectors.map((selector) => (
          <button
            key={`selector-${selector.cell_id}`}
            onMouseEnter={() =>
              selector.isSelectable && handleMouseEnter(selector)
            }
            onClick={() => handleCellClick(selector)}
            className={cn(
              "relative flex h-full w-full items-center justify-center ",
              selector.isSelectable ? "cursor-pointer" : "cursor-default"
            )}
          >
            {/* <p className='opacity-50'>{JSON.stringify(selector.cell_id)}</p> */}
            <ChipStack cellId={selector.cell_id} />
          </button>
        ))}
      </section>

      {/* 2:1 column bets */}
      <section className='lg:col-start-12 lg:row-start-2 lg:row-end-5 grid lg:grid-rows-3 lg:grid-cols-1 grid-cols-3 row-start-12 col-start-2 col-span-3'>
        {twoToOneCells.map((cell, index) => (
          <button
            key={`'2:1' + ${index}`}
            onMouseEnter={() => handleMouseEnter?.(cell)}
            onClick={() => handleCellClick(cell)}
            className='relative flex h-full w-full items-center justify-center'
          >
            <ChipStack cellId={cell.cell_id} />
          </button>
        ))}
      </section>

      {/* Dozen bets */}
      <div className='lg:col-start-2 lg:row-start-1 grid lg:grid-cols-3 lg:col-span-10 lg:grid-rows-1 col-start-1 row-start-2 grid-rows-3 row-span-10 lg:row-span-1'>
        {[firstTwelveCell, secondTwelveCell, thirdTwelveCell].map(
          (text, index) => (
            <button
              key={`index-${index}-${text.cell_id}`}
              onMouseEnter={() => handleMouseEnter?.(text)}
              onClick={() => handleCellClick(text)}
              className='relative flex items-center justify-center py-2 '
            >
              <ChipStack cellId={text.cell_id} />
            </button>
          )
        )}
      </div>

      {/* Even money bets */}
      <div className='lg:col-start-2 lg:row-start-5 grid lg:grid-cols-6 lg:col-span-10 lg:grid-rows-1 row-start-2 col-start-5 grid-rows-6 row-span-10'>
        {[
          oneToEighteen,
          evenCell,
          redCell,
          blackCell,
          oddCell,
          nineteenToThirtySix,
        ].map((text, index) => (
          <button
            key={`index-${index}-${text.cell_id}`}
            onMouseEnter={() => handleMouseEnter?.(text)}
            onClick={() => handleCellClick(text)}
            className='relative flex items-center justify-center py-2 '
          >
            <ChipStack cellId={text.cell_id} />
          </button>
        ))}
      </div>
    </main>
  )
)
