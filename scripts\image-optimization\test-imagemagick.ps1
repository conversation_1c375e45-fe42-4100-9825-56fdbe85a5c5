# Simple ImageMagick Test Script

function Test-ImageMagickInstalled {
    try {
        $version = magick -version 2>$null
        if ($version -and $version -match "ImageMagick") {
            return $true
        }
    }
    catch {
        return $false
    }
    return $false
}

if (Test-ImageMagickInstalled) {
    Write-Host "✅ ImageMagick is installed and available" -ForegroundColor Green
    magick -version
    exit 0
} else {
    Write-Host "❌ ImageMagick is not installed or not available in PATH" -ForegroundColor Red
    Write-Host "ℹ️  Please install ImageMagick manually or run the installation script" -ForegroundColor Cyan
    exit 1
}
