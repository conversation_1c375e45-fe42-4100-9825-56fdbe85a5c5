// Define the red numbers on a roulette table
export const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36]

// Define the table layout - matches the image provided
export const tableLayout = [
  ['3', '6', '9', '12', '15', '18', '21', '24', '27', '30', '33', '36'],
  ['2', '5', '8', '11', '14', '17', '20', '23', '26', '29', '32', '35'],
  ['1', '4', '7', '10', '13', '16', '19', '22', '25', '28', '31', '34'],
]

// Define the dozen bets
export const dozenBets = [
  { id: 'first-12', label: '1<sup class="-mb-2">st</sup>&nbsp;12', type: 'dozen', gradient: 'from-green-800 to-green-950' },
  { id: 'second-12', label: '2<sup class="-mb-2">nd</sup>&nbsp;12', type: 'dozen', gradient: 'from-green-800 to-green-950' },
  { id: 'third-12', label: '3<sup class="-mb-2">rd</sup>&nbsp;12', type: 'dozen', gradient: 'from-green-800 to-green-950' },
]

// Define the even money bets
export const evenMoneyBets = [
  { id: 'low', label: '1-18', type: 'even-money' },
  { id: 'even', label: 'EVEN', type: 'even-money' },
  { id: 'red', label: '', type: 'even-money', isRed: true },
  { id: 'black', label: '', type: 'even-money', isBlack: true },
  { id: 'odd', label: 'ODD', type: 'even-money' },
  { id: 'high', label: '19-36', type: 'even-money' },
]

// Define the column bets
export const columnBets = [
  { id: 'col-1', label: '2 : 1' },
  { id: 'col-2', label: '2 : 1' },
  { id: 'col-3', label: '2 : 1' },
]
