import React from 'react'
import { cn } from '@/lib/utils'

interface DiamondSvgProps {
  color: string
  className?: string
}

export const DiamondSvg: React.FC<DiamondSvgProps> = ({ color, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 30 16"
      className={cn('w-full h-full', className)}
      preserveAspectRatio="xMidYMid meet"
    >
      <polygon
        points="15,1 29,8 15,15 1,8"
        fill={color}
        stroke={color}
        strokeWidth="1"
      />
    </svg>
  )
}
