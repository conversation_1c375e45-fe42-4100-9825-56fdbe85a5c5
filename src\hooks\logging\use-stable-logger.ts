import { useCallback, useMemo, useRef } from "react"
import { logger } from "@/lib/logger"

/**
 * Hook that provides a stable logger reference that won't cause re-renders
 * when used in React components, useEffect dependencies, or useCallback dependencies
 */
export const useStableLogger = (context?: string) => {
  // Use useRef to maintain a stable reference across renders
  const loggerRef = useRef(logger)
  
  // Create stable logging functions that won't change between renders
  const stableLogger = useMemo(() => ({
    debug: (message: string, data?: unknown) => {
      // Use setTimeout to ensure logging happens outside React's render cycle
      setTimeout(() => {
        loggerRef.current.debug(message, { context, data })
      }, 0)
    },
    
    info: (message: string, data?: unknown) => {
      setTimeout(() => {
        loggerRef.current.info(message, { context, data })
      }, 0)
    },
    
    warn: (message: string, data?: unknown) => {
      setTimeout(() => {
        loggerRef.current.warn(message, { context, data })
      }, 0)
    },
    
    error: (message: string, error?: unknown, data?: unknown) => {
      setTimeout(() => {
        loggerRef.current.error(message, error, { context, data })
      }, 0)
    },
    
    logError: (error: Error, additionalMessage?: string, data?: unknown) => {
      setTimeout(() => {
        loggerRef.current.logError(error, additionalMessage, { context, data })
      }, 0)
    }
  }), [context]) // Only recreate if context changes
  
  return stableLogger
}

/**
 * Hook for component-specific logging that includes component name automatically
 */
export const useComponentLogger = (componentName: string) => {
  return useStableLogger(componentName)
}

/**
 * Hook for store-specific logging that includes store name automatically
 */
export const useStoreLogger = (storeName: string) => {
  return useStableLogger(storeName)
}

/**
 * Hook for creating stable logging callbacks that can be safely used in useCallback
 * without causing dependency issues
 */
export const useLoggingCallbacks = (context?: string) => {
  const stableLogger = useStableLogger(context)
  
  return useMemo(() => ({
    logDebug: useCallback((message: string, data?: unknown) => {
      stableLogger.debug(message, data)
    }, [stableLogger]),
    
    logInfo: useCallback((message: string, data?: unknown) => {
      stableLogger.info(message, data)
    }, [stableLogger]),
    
    logWarn: useCallback((message: string, data?: unknown) => {
      stableLogger.warn(message, data)
    }, [stableLogger]),
    
    logError: useCallback((message: string, error?: unknown, data?: unknown) => {
      stableLogger.error(message, error, data)
    }, [stableLogger]),
    
    logException: useCallback((error: Error, additionalMessage?: string, data?: unknown) => {
      stableLogger.logError(error, additionalMessage, data)
    }, [stableLogger])
  }), [stableLogger])
}

/**
 * Performance-optimized logger for use in frequently called functions
 * Uses debouncing to prevent excessive logging
 */
export const useDebouncedLogger = (context?: string, debounceMs: number = 100) => {
  const stableLogger = useStableLogger(context)
  const timeoutRefs = useRef<Record<string, NodeJS.Timeout>>({})
  
  return useMemo(() => ({
    debug: (message: string, data?: unknown) => {
      const key = `debug-${message}`
      if (timeoutRefs.current[key]) {
        clearTimeout(timeoutRefs.current[key])
      }
      timeoutRefs.current[key] = setTimeout(() => {
        stableLogger.debug(message, data)
        delete timeoutRefs.current[key]
      }, debounceMs)
    },
    
    info: (message: string, data?: unknown) => {
      const key = `info-${message}`
      if (timeoutRefs.current[key]) {
        clearTimeout(timeoutRefs.current[key])
      }
      timeoutRefs.current[key] = setTimeout(() => {
        stableLogger.info(message, data)
        delete timeoutRefs.current[key]
      }, debounceMs)
    },
    
    warn: (message: string, data?: unknown) => {
      const key = `warn-${message}`
      if (timeoutRefs.current[key]) {
        clearTimeout(timeoutRefs.current[key])
      }
      timeoutRefs.current[key] = setTimeout(() => {
        stableLogger.warn(message, data)
        delete timeoutRefs.current[key]
      }, debounceMs)
    },
    
    error: (message: string, error?: unknown, data?: unknown) => {
      // Don't debounce errors - they should be logged immediately
      stableLogger.error(message, error, data)
    },
    
    logError: (error: Error, additionalMessage?: string, data?: unknown) => {
      // Don't debounce errors - they should be logged immediately
      stableLogger.logError(error, additionalMessage, data)
    }
  }), [stableLogger, debounceMs])
}

/**
 * Utility to create a logger that's safe to use in Zustand stores
 * without causing re-renders or state subscription issues
 */
export const createStoreLogger = (storeName: string) => {
  return {
    debug: (message: string, data?: unknown) => {
      // Use queueMicrotask for even better performance than setTimeout
      queueMicrotask(() => {
        logger.debug(message, { context: storeName, data })
      })
    },
    
    info: (message: string, data?: unknown) => {
      queueMicrotask(() => {
        logger.info(message, { context: storeName, data })
      })
    },
    
    warn: (message: string, data?: unknown) => {
      queueMicrotask(() => {
        logger.warn(message, { context: storeName, data })
      })
    },
    
    error: (message: string, error?: unknown, data?: unknown) => {
      queueMicrotask(() => {
        logger.error(message, error, { context: storeName, data })
      })
    },
    
    logError: (error: Error, additionalMessage?: string, data?: unknown) => {
      queueMicrotask(() => {
        logger.logError(error, additionalMessage, { context: storeName, data })
      })
    }
  }
}
