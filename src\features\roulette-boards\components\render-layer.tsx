import React, { useEffect, useRef, useState } from "react"
import { useHistoryQuery } from "@/hooks"
import { useMobile } from "@/hooks/use-mobile"
import { useTableTextSize } from "@/hooks/use-table-text-size"
import { cn } from "@/lib/utils"
import { useGameStateStore } from "@/stores/game-state-store"
import { SelectableCell, twoToOneCells } from "@/config/selector-config"
import {
  blackCell,
  evenCell,
  firstTwelveCell,
  nineteenToThirtySix,
  oddCell,
  oneToEighteen,
  redCell,
  secondTwelveCell,
  thirdTwelveCell,
} from "@/config/selector-config"
import { DiamondSvg } from "./diamond-svg"
import {
  redNumbers,
  tableLayout,
  dozenBets,
  columnBets,
  evenMoneyBets,
} from "../constants/table-constants"
import { STYLES, getNumberColorClass } from "../styles/table-styles"

export interface RenderLayerProps {
  hoveredCell: SelectableCell | null
  className?: string
}

// Create a mobile table layout by transposing the desktop layout (rotating 90 degrees)
const mobileTableLayout = [
  ["1", "2", "3"],
  ["4", "5", "6"],
  ["7", "8", "9"],
  ["10", "11", "12"],
  ["13", "14", "15"],
  ["16", "17", "18"],
  ["19", "20", "21"],
  ["22", "23", "24"],
  ["25", "26", "27"],
  ["28", "29", "30"],
  ["31", "32", "33"],
  ["34", "35", "36"],
]

export const RenderLayer: React.FC<RenderLayerProps> = ({
  hoveredCell,
  className,
}) => {
  const [timedHoverCell, setTimedHoverCell] = useState<SelectableCell | null>(
    null
  )
  const { data: history = [] } = useHistoryQuery(250)
  const timingActive = import.meta.env.VITE_APP_BUILD === "retail"
  const timerRef = useRef<number | null>(null)
  const isMobile = useMobile()
  const { textSizes, containerRef } = useTableTextSize()

  useEffect(() => {
    // Clear any existing timer
    if (timerRef.current !== null) {
      window.clearTimeout(timerRef.current)
      timerRef.current = null
    }

    // Set the cell immediately
    setTimedHoverCell(hoveredCell)

    // Only set up timer if we're showing a cell and timing is active
    if (hoveredCell !== null && timingActive) {
      timerRef.current = window.setTimeout(() => {
        setTimedHoverCell(null)
        timerRef.current = null
      }, 400)
    }

    return () => {
      if (timerRef.current !== null) {
        window.clearTimeout(timerRef.current)
        timerRef.current = null
      }
    }
  }, [hoveredCell, timingActive])

  const PastNumber = ({
    occurrenceCount,
    tableNumber,
  }: {
    occurrenceCount: number
    tableNumber: number
  }) => (
    <div
      className={cn(
        tableNumber === 0 && !isMobile ? "top-1/3" : "top-0",
        "absolute text-center lg:text-base text-xs rounded-lg lg:leading-normal leading-3 right-0 lg:w-full px-1 w-auto border-2 border-green-500 text-green-500 bg-black/50 z-5"
      )}
    >
      {occurrenceCount}
    </div>
  )

  const HotOrCold = ({
    isHot,
    isCold,
    tableNumber,
  }: {
    isHot: boolean
    isCold: boolean
    tableNumber: number
  }) => (
    <div
      className={cn(
        tableNumber === 0 ? "bottom-1/3" : "bottom-0",
        "absolute opacity-80 text-center text-base rounded-lg left-0 w-full lg:-bottom-3 -bottom-1.5 z-5"
      )}
    >
      {isCold && <img src='/assets/images/misc/blue-flame.webp' alt='Cold' />}
      {isHot && <img src='/assets/images/misc/red-flame.webp' alt='Hot' />}
    </div>
  )

  const PastNumbers = ({ tableNumber }: { tableNumber: number }) => {
    const { showPastNumbers, showHotCold } = useGameStateStore()

    // Count occurrences of this tableNumber
    const occurrenceCount = history.filter(
      (h) => h.rouletteNumber === tableNumber
    ).length

    // Create a frequency map of all numbers that appear in history
    const frequencyMap = history.reduce((acc, h) => {
      acc[h.rouletteNumber] = (acc[h.rouletteNumber] || 0) + 1
      return acc
    }, {} as Record<number, number>)

    // Convert to array of [number, count] pairs and sort by frequency
    const sortedFrequencies = Object.entries(frequencyMap)
      .map(([num, count]) => [parseInt(num), count])
      .sort((a, b) => a[1] - b[1]) // Sort by frequency (ascending)

    // Get the 4 coldest and hottest numbers
    const coldNumbers = sortedFrequencies.slice(0, 4).map((pair) => pair[0])
    const hotNumbers = sortedFrequencies.slice(-4).map((pair) => pair[0])

    // Determine if this tableNumber is hot or cold
    const isCold = coldNumbers.includes(tableNumber) && occurrenceCount > 0
    const isHot = hotNumbers.includes(tableNumber)

    if (occurrenceCount === 0) return null
    if (!showPastNumbers && !showHotCold) return null

    if (showPastNumbers && showHotCold) {
      return (
        <>
          <PastNumber
            tableNumber={tableNumber}
            occurrenceCount={occurrenceCount}
          />
          <HotOrCold tableNumber={tableNumber} isHot={isHot} isCold={isCold} />
        </>
      )
    }

    if (showPastNumbers) {
      return (
        <PastNumber
          tableNumber={tableNumber}
          occurrenceCount={occurrenceCount}
        />
      )
    }
    if (showHotCold) {
      return (
        <HotOrCold tableNumber={tableNumber} isHot={isHot} isCold={isCold} />
      )
    }
  }

  return (
    <div className={cn(STYLES.mainContainer, className)} ref={containerRef}>
      {/* Text size config indicator - only visible during development */}
      {import.meta.env.DEV && (
        <div className='absolute top-0 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded-bl-md z-50'>
          Size: {textSizes.configTag}
        </div>
      )}
      {/* Main table container with cohesive grid */}
      {isMobile ? (
        // Mobile layout - rotated 90 degrees
        <div
          className={cn(
            "w-full h-full bg-black/30 rounded-lg relative p-0.5 grid gap-0.5",
            "grid-cols-[minmax(40px,0.7fr)_repeat(3,1fr)_minmax(40px,0.7fr)]",
            "grid-rows-[1.125fr_repeat(12,1fr)_1.125fr]"
          )}
        >
          {/* Zero - spans 3 columns and 1 row in mobile */}
          <div
            className={cn(
              STYLES.cellBase,
              STYLES.cellText,
              STYLES.colors.green,
              textSizes.zero,
              "relative col-start-2 col-span-3 row-start-1 row-span-1",
              // Only highlight zero when directly hovering over it
              timedHoverCell?.cell_id === 0 && "bg-green-800"
            )}
          >
            <p className='z-6'>0</p>
            <PastNumbers tableNumber={0} />
          </div>

          {/* Dozen bets - left column */}
          <div className='col-start-1 row-start-2 row-span-12 grid grid-rows-3 gap-0.5'>
            {/* First dozen */}
            <div
              key={dozenBets[0].id}
              className={cn(
                STYLES.cellBase,
                STYLES.cellText,
                STYLES.colors.greyGradient,
                textSizes.dozen,
                "text-center flex items-center justify-center",
                timedHoverCell?.cell_id === firstTwelveCell.cell_id &&
                  "bg-gray-700"
              )}
            >
              <div className='transform -rotate-90 w-full h-full flex items-center justify-center overflow-hidden'>
                <span
                  className='whitespace-nowrap text-center'
                  dangerouslySetInnerHTML={{ __html: dozenBets[0].label }}
                />
              </div>
            </div>

            {/* Second dozen */}
            <div
              key={dozenBets[1].id}
              className={cn(
                STYLES.cellBase,
                STYLES.cellText,
                STYLES.colors.greyGradient,
                textSizes.dozen,
                "text-center flex items-center justify-center",
                timedHoverCell?.cell_id === secondTwelveCell.cell_id &&
                  "bg-gray-700"
              )}
            >
              <div className='transform -rotate-90 w-full h-full flex items-center justify-center overflow-hidden'>
                <span
                  className='whitespace-nowrap text-center'
                  dangerouslySetInnerHTML={{ __html: dozenBets[1].label }}
                />
              </div>
            </div>

            {/* Third dozen */}
            <div
              key={dozenBets[2].id}
              className={cn(
                STYLES.cellBase,
                STYLES.cellText,
                STYLES.colors.greyGradient,
                textSizes.dozen,
                "text-center flex items-center justify-center",
                timedHoverCell?.cell_id === thirdTwelveCell.cell_id &&
                  "bg-gray-700"
              )}
            >
              <div className='transform -rotate-90 w-full h-full flex items-center justify-center overflow-hidden'>
                <span
                  className='whitespace-nowrap text-center'
                  dangerouslySetInnerHTML={{ __html: dozenBets[2].label }}
                />
              </div>
            </div>
          </div>

          {/* Numbers grid - middle section */}
          {mobileTableLayout.map((row, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {row.map((num, colIndex) => (
                <div
                  key={`num-${num}`}
                  className={cn(
                    STYLES.cellBase,
                    STYLES.cellText,
                    textSizes.numbers,
                    getNumberColorClass(num, redNumbers),
                    "row-start-" + (rowIndex + 2), // Start from row 2 (after zero)
                    "col-start-" + (colIndex + 2), // Start from column 2 (after dozens)
                    // Highlight numbers only when directly hovering over them
                    // or when hovering over a special bet that includes this number
                    timedHoverCell?.cell_selectors?.includes(num) &&
                      (timedHoverCell?.cell_id === redCell.cell_id ||
                        timedHoverCell?.cell_id === blackCell.cell_id ||
                        timedHoverCell?.cell_id === evenCell.cell_id ||
                        timedHoverCell?.cell_id === oddCell.cell_id ||
                        timedHoverCell?.cell_id === oneToEighteen.cell_id ||
                        timedHoverCell?.cell_id ===
                          nineteenToThirtySix.cell_id ||
                        timedHoverCell?.cell_id === firstTwelveCell.cell_id ||
                        timedHoverCell?.cell_id === secondTwelveCell.cell_id ||
                        timedHoverCell?.cell_id === thirdTwelveCell.cell_id ||
                        twoToOneCells.some(
                          (cell) => cell.cell_id === timedHoverCell?.cell_id
                        ) ||
                        timedHoverCell?.cell_id === parseInt(num)) &&
                      (redNumbers.includes(parseInt(num))
                        ? "bg-red-800"
                        : "bg-gray-800")
                  )}
                >
                  {num}
                  <PastNumbers tableNumber={parseInt(num)} />
                </div>
              ))}
            </React.Fragment>
          ))}

          {/* Column bets - right side (now bottom) */}
          {columnBets.map((bet, index) => (
            <div
              key={bet.id}
              className={cn(
                STYLES.cellBase,
                STYLES.cellText,
                STYLES.colors.greyGradient,
                textSizes.column,
                "leading-tight text-center flex items-center justify-center",
                "col-start-" + (index + 2), // Position each bet horizontally
                "col-span-1",
                "row-start-14", // Bottom row
                // Only highlight when the specific column bet is hovered
                timedHoverCell?.cell_id === twoToOneCells[index]?.cell_id &&
                  "bg-gray-700"
              )}
            >
              <div className='transform w-full whitespace-nowrap h-full flex items-center justify-center'>
                {bet.label}
              </div>
            </div>
          ))}

          {/* Even money bets - right column */}
          <div className='col-start-5 row-start-2 row-span-12 grid grid-rows-6 gap-0.5'>
            {evenMoneyBets.map((bet, index) => (
              <div
                key={bet.id}
                className={cn(
                  STYLES.cellBase,
                  STYLES.cellText,
                  textSizes.evenMoney,
                  "text-center leading-tight",
                  "row-start-" + (index + 1), // Position each bet
                  "row-span-1",
                  STYLES.colors.greyGradient,
                  // Apply highlighting based on the bet type
                  // Only highlight the specific bet area being hovered over
                  // and highlight the numbers that are part of that bet
                  bet.isRed &&
                    timedHoverCell?.cell_id === redCell.cell_id &&
                    "bg-green-800",
                  bet.isBlack &&
                    timedHoverCell?.cell_id === blackCell.cell_id &&
                    "bg-green-800",
                  bet.id === "even" &&
                    timedHoverCell?.cell_id === evenCell.cell_id &&
                    "bg-green-800",
                  bet.id === "odd" &&
                    timedHoverCell?.cell_id === oddCell.cell_id &&
                    "bg-green-800",
                  bet.id === "low" &&
                    timedHoverCell?.cell_id === oneToEighteen.cell_id &&
                    "bg-green-800",
                  bet.id === "high" &&
                    timedHoverCell?.cell_id === nineteenToThirtySix.cell_id &&
                    "bg-green-800"
                )}
              >
                {bet.isRed ? (
                  <div className='w-auto h-full rotate-90 flex items-center justify-center'>
                    <DiamondSvg color='#CC0000' />
                  </div>
                ) : bet.isBlack ? (
                  <div className='w-auto h-full rotate-90 flex items-center justify-center'>
                    <DiamondSvg color='#000000' />
                  </div>
                ) : (
                  <div className='transform rotate-90 w-full h-full flex items-center justify-center overflow-hidden'>
                    <span className='whitespace-nowrap text-center'>
                      {bet.label}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ) : (
        // Desktop layout - original
        <div className={STYLES.tableGrid}>
          {/* Zero - spans 1 column and 3 rows */}
          <div
            className={cn(
              STYLES.cellBase,
              STYLES.cellText,
              STYLES.colors.green,
              textSizes.zero,
              "col-start-1 col-span-1 row-start-2 row-span-3 ",
              // Only highlight zero when directly hovering over it
              timedHoverCell?.cell_id === 0 && "bg-green-800"
            )}
          >
            <p className='z-6'>0</p> <PastNumbers tableNumber={0} />
          </div>

          {/* First dozen - top row */}
          <div
            key={dozenBets[0].id}
            className={cn(
              STYLES.cellBase,
              STYLES.cellText,
              STYLES.colors.greyGradient,
              textSizes.dozen,
              "text-center leading-tight col-start-2 col-span-4 row-start-1",
              timedHoverCell?.cell_id === firstTwelveCell.cell_id &&
                "bg-gray-700"
            )}
            dangerouslySetInnerHTML={{ __html: dozenBets[0].label }}
          />

          {/* Second dozen - top row */}
          <div
            key={dozenBets[1].id}
            className={cn(
              STYLES.cellBase,
              STYLES.cellText,
              STYLES.colors.greyGradient,
              textSizes.dozen,
              "text-center leading-tight col-start-6 col-span-4 row-start-1",
              timedHoverCell?.cell_id === secondTwelveCell.cell_id &&
                "bg-gray-700"
            )}
            dangerouslySetInnerHTML={{ __html: dozenBets[1].label }}
          />

          {/* Third dozen - top row */}
          <div
            key={dozenBets[2].id}
            className={cn(
              STYLES.cellBase,
              STYLES.cellText,
              STYLES.colors.greyGradient,
              textSizes.dozen,
              "text-center leading-tight col-start-10 col-span-4 row-start-1",
              timedHoverCell?.cell_id === thirdTwelveCell.cell_id &&
                "bg-gray-700"
            )}
            dangerouslySetInnerHTML={{ __html: dozenBets[2].label }}
          />

          {/* Numbers grid - middle section */}
          {tableLayout.map((row, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {row.map((num, colIndex) => (
                <div
                  key={`num-${num}`}
                  className={cn(
                    STYLES.cellBase,
                    STYLES.cellText,
                    textSizes.numbers,
                    getNumberColorClass(num, redNumbers),
                    "col-start-" + (colIndex + 2), // Start from column 2 (after zero)
                    "row-start-" + (rowIndex + 2), // Start from row 2 (after dozens)
                    // Highlight numbers only when directly hovering over them
                    // or when hovering over a special bet that includes this number
                    timedHoverCell?.cell_selectors?.includes(num) &&
                      (timedHoverCell?.cell_id === redCell.cell_id ||
                        timedHoverCell?.cell_id === blackCell.cell_id ||
                        timedHoverCell?.cell_id === evenCell.cell_id ||
                        timedHoverCell?.cell_id === oddCell.cell_id ||
                        timedHoverCell?.cell_id === oneToEighteen.cell_id ||
                        timedHoverCell?.cell_id ===
                          nineteenToThirtySix.cell_id ||
                        timedHoverCell?.cell_id === firstTwelveCell.cell_id ||
                        timedHoverCell?.cell_id === secondTwelveCell.cell_id ||
                        timedHoverCell?.cell_id === thirdTwelveCell.cell_id ||
                        twoToOneCells.some(
                          (cell) => cell.cell_id === timedHoverCell?.cell_id
                        ) ||
                        timedHoverCell?.cell_id === parseInt(num)) &&
                      (redNumbers.includes(parseInt(num))
                        ? "bg-red-800"
                        : "bg-gray-800")
                  )}
                >
                  <p className='z-6'>{num}</p>
                  <PastNumbers tableNumber={parseInt(num)} />
                </div>
              ))}
            </React.Fragment>
          ))}

          {/* Column bets - right side */}
          {columnBets.map((bet, index) => (
            <div
              key={bet.id}
              className={cn(
                STYLES.cellBase,
                STYLES.cellText,
                STYLES.colors.greyGradient,
                textSizes.column,
                "leading-tight",
                "col-start-14 row-start-" + (index + 2), // Start from row 2 (after dozens)
                // Only highlight when the specific column bet is hovered
                timedHoverCell?.cell_id === twoToOneCells[index]?.cell_id &&
                  "bg-gray-700"
              )}
            >
              {bet.label}
            </div>
          ))}

          {/* Even money bets - bottom row */}
          {evenMoneyBets.map((bet, index) => (
            <div
              key={bet.id}
              className={cn(
                STYLES.cellBase,
                STYLES.cellText,
                textSizes.evenMoney,
                "text-center leading-tight",
                "col-start-" + (index * 2 + 2), // Position each bet with 2 columns spacing
                "col-span-2",
                "row-start-5",
                STYLES.colors.greyGradient,
                // Apply highlighting based on the bet type
                // Only highlight the specific bet area being hovered over
                // and highlight the numbers that are part of that bet
                bet.isRed &&
                  timedHoverCell?.cell_id === redCell.cell_id &&
                  "bg-green-800",
                bet.isBlack &&
                  timedHoverCell?.cell_id === blackCell.cell_id &&
                  "bg-green-800",
                bet.id === "even" &&
                  timedHoverCell?.cell_id === evenCell.cell_id &&
                  "bg-green-800",
                bet.id === "odd" &&
                  timedHoverCell?.cell_id === oddCell.cell_id &&
                  "bg-green-800",
                bet.id === "low" &&
                  timedHoverCell?.cell_id === oneToEighteen.cell_id &&
                  "bg-green-800",
                bet.id === "high" &&
                  timedHoverCell?.cell_id === nineteenToThirtySix.cell_id &&
                  "bg-green-800"
              )}
            >
              {bet.isRed ? (
                <div className='w-auto h-20'>
                  <DiamondSvg color='#CC0000' />
                </div>
              ) : bet.isBlack ? (
                <div className='w-auto h-20'>
                  <DiamondSvg color='#000000' />
                </div>
              ) : (
                bet.label
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
