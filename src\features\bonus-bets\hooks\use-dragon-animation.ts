import { useEffect, useRef, useCallback } from "react"

interface DragonAnimationConfig {
  /** Total number of bonus numbers/frames in the sprite sheet */
  totalFrames: number
  /** Height of each frame in pixels */
  frameHeight: number
  /** The winning bonus number to stop on (0-based index) */
  targetBonusNumber?: number | null
  /** Duration of the settle animation in milliseconds */
  settleAnimationDuration?: number
}

/**
 * Hook to manage dragon sprite sheet scroll animation with precise stopping
 */
export const useDragonAnimation = (
  isSpinning: boolean,
  config: DragonAnimationConfig
) => {
  const spritesheetRef = useRef<HTMLImageElement>(null)
  const animationRef = useRef<number>(0)
  const scrollPositionRef = useRef(0)
  const refreshRateRef = useRef<number>(60)
  const frameTimesRef = useRef<number[]>([])
  const isSettlingRef = useRef(false)
  const settleStartTimeRef = useRef<number>(0)
  const settleStartPositionRef = useRef<number>(0)
  const targetPositionRef = useRef<number>(0)

  const {
    totalFrames,
    frameHeight,
    targetBonusNumber,
    settleAnimationDuration = 2000,
  } = config

  // Calculate target position based on bonus number
  const calculateTargetPosition = useCallback(
    (bonusNumber: number) => {
      // Ensure we stay within bounds
      const clampedNumber = Math.max(0, Math.min(bonusNumber, totalFrames - 1))
      return clampedNumber * frameHeight
    },
    [totalFrames, frameHeight]
  )

  // Easing function for smooth settle animation
  const easeOutCubic = useCallback((t: number) => {
    return 1 - Math.pow(1 - t, 3)
  }, [])

  useEffect(() => {
    const REFRESH_RATE_MAP = {
      30: 0.5,
      60: 1,
      75: 1.25,
      90: 1.5,
      120: 2,
      144: 2.4,
      240: 4,
    } as const

    const detectRefreshRate = (timestamp: number) => {
      frameTimesRef.current.push(timestamp)

      if (frameTimesRef.current.length > 10) {
        frameTimesRef.current.shift()
      }

      if (frameTimesRef.current.length >= 5) {
        const frameTimes = frameTimesRef.current
        const avgFrameTime =
          (frameTimes[frameTimes.length - 1] - frameTimes[0]) /
          (frameTimes.length - 1)
        const estimatedFPS = Math.round(1000 / avgFrameTime)

        const rates = Object.keys(REFRESH_RATE_MAP).map(Number)
        const closest = rates.reduce((prev, curr) =>
          Math.abs(curr - estimatedFPS) < Math.abs(prev - estimatedFPS)
            ? curr
            : prev
        )

        refreshRateRef.current = closest
        return REFRESH_RATE_MAP[closest as keyof typeof REFRESH_RATE_MAP]
      }

      return 1
    }

    const animateScroll = (timestamp: number) => {
      if (!spritesheetRef.current) return

      // Handle settling animation when we have a target
      if (
        isSettlingRef.current &&
        targetBonusNumber !== null &&
        targetBonusNumber !== undefined
      ) {
        const elapsed = timestamp - settleStartTimeRef.current
        const progress = Math.min(elapsed / settleAnimationDuration, 1)
        const easedProgress = easeOutCubic(progress)

        const currentPosition =
          settleStartPositionRef.current +
          (targetPositionRef.current - settleStartPositionRef.current) *
            easedProgress

        scrollPositionRef.current = currentPosition
        spritesheetRef.current.style.transform = `translateY(-${currentPosition}px)`

        if (progress >= 1) {
          // Settling complete
          isSettlingRef.current = false
          return
        }

        animationRef.current = requestAnimationFrame(animateScroll)
        return
      }

      // Normal spinning animation
      if (isSpinning) {
        const scrollSpeed = detectRefreshRate(timestamp)
        scrollPositionRef.current += scrollSpeed

        // Keep scrolling within sprite sheet bounds
        const maxScroll = totalFrames * frameHeight - frameHeight
        if (scrollPositionRef.current > maxScroll) {
          scrollPositionRef.current = 0 // Loop back to start
        }

        spritesheetRef.current.style.transform = `translateY(-${scrollPositionRef.current}px)`
        animationRef.current = requestAnimationFrame(animateScroll)
      }
    }

    // Start spinning
    if (isSpinning && !isSettlingRef.current) {
      frameTimesRef.current = []
      animationRef.current = requestAnimationFrame(animateScroll)
    }
    // Stop spinning and settle to target
    else if (
      !isSpinning &&
      targetBonusNumber !== null &&
      targetBonusNumber !== undefined
    ) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }

      // Start settling animation
      isSettlingRef.current = true
      settleStartTimeRef.current = performance.now()
      settleStartPositionRef.current = scrollPositionRef.current
      targetPositionRef.current = calculateTargetPosition(targetBonusNumber)

      animationRef.current = requestAnimationFrame(animateScroll)
    }
    // Reset everything
    else if (
      !isSpinning &&
      (targetBonusNumber === null || targetBonusNumber === undefined)
    ) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      scrollPositionRef.current = 0
      frameTimesRef.current = []
      isSettlingRef.current = false
      if (spritesheetRef.current) {
        spritesheetRef.current.style.transform = "translateY(0px)"
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [
    isSpinning,
    targetBonusNumber,
    totalFrames,
    frameHeight,
    settleAnimationDuration,
    calculateTargetPosition,
    easeOutCubic,
  ])

  // Method to manually set position (useful for testing)
  const setPosition = useCallback(
    (bonusNumber: number) => {
      if (spritesheetRef.current) {
        const position = calculateTargetPosition(bonusNumber)
        scrollPositionRef.current = position
        spritesheetRef.current.style.transform = `translateY(-${position}px)`
      }
    },
    [calculateTargetPosition]
  )

  return {
    spritesheetRef,
    setPosition,
    isSettling: isSettlingRef.current,
  }
}
