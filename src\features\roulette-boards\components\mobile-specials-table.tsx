import { useRef, useState } from "react"
import { useRoundPhase } from "@/hooks"
import { EnhancedErrorBoundary } from "@/middleware"
import { useBettingStore } from "@/stores/betting-store"
import { useSettingsStore } from "@/stores/settings-store"
import { rouletteSelectors, SelectableCell } from "@/config/selector-config"
import { Zeros, Orphalins, Tiers, Voisins } from "@/config/specials-config"

// Inner component that will be wrapped with error boundary
const MobileSpecialsTableInner: React.FC = () => {
  const svgRef = useRef<SVGSVGElement>(null)
  const [highlightedValues, setHighlightedValues] = useState<number[]>([])
  const initialNeighbours = useSettingsStore((state) => state.initialNeighbours)
  const addChip = useBettingStore((state) => state.addChip)
  const { selectedChip, placedChips } = useBettingStore()
  const { Betting } = useRoundPhase()

  const selectors: SelectableCell[] = rouletteSelectors

  // The highlight array defining the circular arrangement
  const highlightArr = [
    16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26, 0, 32, 15, 19,
    4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24,
  ]

  const getNeighbors = (index: number) => {
    const currentIndex = highlightArr.indexOf(index)
    const length = highlightArr.length

    const prevIndex = (currentIndex - initialNeighbours + length) % length
    const nextIndex = (currentIndex + initialNeighbours) % length

    return { prevIndex, nextIndex }
  }

  const getHighlightedIndices = (prevIndex: number, nextIndex: number) => {
    const length = highlightArr.length

    if (prevIndex < nextIndex) {
      return highlightArr.slice(prevIndex, nextIndex + 1)
    }

    return [
      ...highlightArr.slice(prevIndex, length),
      ...highlightArr.slice(0, nextIndex + 1),
    ]
  }

  const handleClick = (event: React.MouseEvent<SVGPathElement>) => {
    if (!selectedChip || !Betting) return
    const cell = event.target as SVGSVGElement
    const splitCells = parseInt(cell.id.split("-")[0])
    const { prevIndex, nextIndex } = getNeighbors(splitCells)
    const highlightedCells = getHighlightedIndices(prevIndex, nextIndex)

    // Update highlighted values for visual feedback
    setHighlightedValues(highlightedCells)

    // Clear highlights after a short delay for visual feedback
    setTimeout(() => {
      setHighlightedValues([])
    }, 300)

    if (highlightedCells.includes(0)) {
      addChip(
        {
          cell_id: 0,
          chips_placed: [],
          bet_type_id: 1,
          cell_selectors: ["0"],
          isSelectable: true,
        },
        selectedChip
      )
    }

    // Find and add chips to placedChips
    selectors.forEach((selector) => {
      if (
        selector.cell_selectors.some(
          (value) =>
            highlightedCells.includes(parseInt(value)) &&
            selector.cell_selectors.length === 1
        )
      ) {
        addChip(selector, selectedChip)
      }
    })
  }

  const getPathFill = (cellId: string) => {
    const green = "hsl(122, 87%, 36%)"
    const darkGreen = "hsl(122, 87%, 30%)"
    const black = "hsl(0, 0%, 15%)"
    const red = "	hsl(0, 79%, 42%)"
    const darkRed = "	hsl(0, 79%, 35%)"
    const richBlack = "	hsl(0, 0%, 9%)"

    // Define red numbers (same as in table-constants.ts)
    const redNumbers = [
      1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36,
    ]

    // Check if cell is highlighted (clicked/tapped)
    const isHighlighted = highlightedValues.includes(parseInt(cellId))

    // Determine base color based on cell ID
    const cellNum = parseInt(cellId)

    // Special case for zero
    if (cellNum === 0) {
      return isHighlighted ? darkGreen : green
    }

    // For red numbers
    if (redNumbers.includes(cellNum)) {
      return isHighlighted ? darkRed : red
    }

    // For black numbers
    return isHighlighted ? richBlack : black
  }

  const handleSpecialButtonClick = (cells: SelectableCell[]) => {
    if (!selectedChip || !Betting) return
    cells.forEach((cell) => {
      addChip(cell, selectedChip)
    })
  }

  return (
    <svg
      ref={svgRef}
      xmlns='http://www.w3.org/2000/svg'
      width={"100%"}
      height={"100%"}
      viewBox='0 0 239.08 590'
    >
      <defs>
        <style>
          {
            ".cls-8{fill:#252525; transition: all 0.3s ease;}.cls-13{fill:#fff; user-select:none; pointer-events: none; cursor: default; transition: all 0.3s ease;}.cls-18{fill:#be1616; transition: all 0.3s ease;}"
          }
        </style>
      </defs>
      <rect
        id='background'
        x={0}
        width={"100%"}
        height={"100%"}
        rx={119.54}
        ry={119.54}
        style={{
          opacity: 0.75,
        }}
      />
      <path d='M239.08 102.6C239.08 45.19 184.15 0 119.71 0S.2 43.88.17 102.6L0 487.96C-.02 543.19 55.26 590 119.71 590s119.37-46.37 119.37-101.6zm-41.63 20.34.24 356.34c.02 33.82-31.11 69.01-78.15 68.78-49.48 2.24-80.42-44.13-80.42-67.52V141.93c-1.34-72.06 27.46-102.44 80.6-103.7 48.01-1.14 80.74 40.4 77.74 84.72Z' />
      <path
        id='33-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("33"),
        }}
        data-name='33-cell'
        className='cls-8'
        d='M202.38 149.01c0 1.29 1.1 2.34 2.47 2.34h29.74c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.83c-1.4 0-2.5 1.1-2.42 2.42.03.55.05 1.1.05 1.66V149Z'
      />
      <path
        id='1-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("1"),
        }}
        data-name='1-cell'
        className='cls-18'
        d='M202.26 182.14c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='20-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("20"),
        }}
        data-name='20-cell'
        className='cls-8'
        d='M202.26 215.27c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='14-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("14"),
        }}
        data-name='14-cell'
        className='cls-18'
        d='M202.26 248.39c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='31-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("31"),
        }}
        data-name='31-cell'
        className='cls-8'
        d='M202.26 281.52c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34V253.9c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='9-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("9"),
        }}
        data-name='9-cell'
        className='cls-18'
        d='M202.26 314.64c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='22-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("22"),
        }}
        data-name='22-cell'
        className='cls-8'
        d='M202.26 347.77c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='18-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("18"),
        }}
        data-name='18-cell'
        className='cls-18'
        d='M202.26 380.89c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='29-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("29"),
        }}
        data-name='29-cell'
        className='cls-8'
        d='M202.26 414.02c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34V386.4c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='7-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("7"),
        }}
        data-name='7-cell'
        className='cls-18'
        d='M202.26 447.14c0 1.29 1.1 2.34 2.47 2.34h29.93c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.93c-1.36 0-2.47 1.05-2.47 2.34z'
      />
      <path
        id='28-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("28"),
        }}
        data-name='28-cell'
        className='cls-8'
        d='M202.28 480.16c-.02 1.33 1.12 2.45 2.53 2.45h29.81c1.36 0 2.47-1.05 2.47-2.34v-27.62c0-1.29-1.1-2.34-2.47-2.34h-29.75c-1.36 0-2.47 1.05-2.47 2.34v18.94c0 .67-.07 5.51-.12 8.58Z'
      />
      <path
        id='12-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("12"),
        }}
        data-name='12-cell'
        className='cls-18'
        d='M204.01 484.79c-1.2 0-2.2.87-2.39 2.03-1.43 8.5-3.93 14.25-7.33 20.32-.82 1.11-.59 2.68.56 3.45l26.72 18.33c1.09.72 2.57.44 3.26-.65 8.49-13.26 10.87-26.39 11.31-41.11.04-1.31-1.04-2.38-2.37-2.38h-29.78Z'
      />
      <path
        id='35-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("35"),
        }}
        data-name='35-cell'
        className='cls-8'
        d='M173.28 534c-1.03.83-1.27 2.32-.47 3.37l18.74 24.44c.76 1 2.17 1.24 3.2.54 11.78-7.99 19.55-16 26.97-27.33.69-1.05.38-2.45-.67-3.15l-26.66-19.24a2.423 2.423 0 0 0-3.26.53c-3.59 6.82-11.2 16.52-17.84 20.83Z'
      />
      <path
        id='3-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("3"),
        }}
        data-name='3-cell'
        className='cls-18'
        d='M144.69 548.75c-1.26.37-2.03 1.64-1.69 2.89l7.66 29.37c.32 1.23 1.6 1.99 2.86 1.66 12.59-3.3 25.57-9.23 36.05-15.68 1.15-.71 1.44-2.23.63-3.31l-19.04-25.08c-.76-1.02-2.2-1.27-3.28-.59-6.75 4.22-15.35 8.47-23.19 10.75Z'
      />
      <path
        id='26-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("26"),
        }}
        data-name='26-cell'
        className='cls-8'
        d='M106.5 550.57c-1.28-.12-2.42.76-2.61 2.01l-5.46 30.19c-.19 1.31.76 2.52 2.1 2.66 4.55 1.01 19.29 1.49 23.43 1.49 6.79 0 14.03-1.05 22.76-2.86a2.31 2.31 0 0 0 1.78-2.85l-7.4-29.53c-.32-1.23-1.59-1.98-2.86-1.71-3.93 1.25-9.82 1.84-15.9 1.84-4.13 0-11.68-.85-15.84-1.25Z'
      />
      <path
        id='0-cell'
        onClick={handleClick}
        data-name='0-cell'
        d='M101.72 552.43c.23-1.28-.64-2.51-1.94-2.77-7.94-1.61-18.34-5.11-25.66-10.67-1.2-.91-2.97-.65-3.74.63l-15.71 27.49c-.6 1-.37 2.28.61 2.93 6.34 4.28 16.93 8.68 17.79 9.02.03.02.07.03.1.04.76.33 10.09 4.41 20.51 5.92 1.24.18 2.37-.66 2.59-1.87z'
        style={{
          fill: getPathFill("0"),
        }}
      />
      <path
        id='32-cell'
        data-name='32-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("32"),
        }}
        className='cls-18'
        d='M51.44 517.08c-.71-1.04-2.1-1.43-3.23-.84l-29.98 15.73c-1.11.58-1.58 1.92-1 3.02 6.48 12.24 19.64 22.63 32.49 31.32 1.12.76 2.66.41 3.35-.75l15.77-27.74c.61-1.03.34-2.35-.62-3.08-6.19-4.68-12.24-10.93-16.8-17.66Z'
      />
      <path
        id='15-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("15"),
        }}
        data-name='15-cell'
        className='cls-8'
        d='M36.59 486.06c-.22-1.27-1.3-2.32-2.61-2.32l-29.52.11c-1.29 0-2.33 1-2.32 2.27.02 4.2.36 13.43 2.55 20.71 2.02 6.71 5.62 15.85 8.94 22.38.59 1.17 2.04 1.61 3.22 1.01l28.81-15.21c1.34-.7 1.72-2.47.89-3.73-2.87-4-8.79-18.44-9.96-25.21Z'
      />
      <path
        id='19-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("19"),
        }}
        data-name='19-cell'
        className='cls-18'
        d='M36.1 452.65c0-1.29-1.05-2.34-2.35-2.34H5.52c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.32c1.33 0 2.37-1.1 2.3-2.42-.02-.42-.03-.85-.03-1.28v-26.26Z'
      />
      <path
        id='4-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("4"),
        }}
        data-name='4-cell'
        className='cls-8'
        d='M36.23 419.52c0-1.29-1.05-2.34-2.35-2.34H5.49c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34v-27.62Z'
      />
      <path
        id='21-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("21"),
        }}
        data-name='21-cell'
        className='cls-18'
        d='M36.19 386.4c0-1.29-1.05-2.34-2.35-2.34H5.45c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34V386.4Z'
      />
      <path
        id='2-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("2"),
        }}
        data-name='2-cell'
        className='cls-8'
        d='M36.15 353.27c0-1.29-1.05-2.34-2.35-2.34H5.41c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34v-27.62Z'
      />
      <path
        id='25-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("25"),
        }}
        data-name='25-cell'
        className='cls-18'
        d='M36.12 320.15c0-1.29-1.05-2.34-2.35-2.34H5.37c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34z'
      />
      <path
        id='17-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("17"),
        }}
        data-name='17-cell'
        className='cls-8'
        d='M36.08 287.02c0-1.29-1.05-2.34-2.35-2.34H5.34c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34v-27.62Z'
      />
      <path
        id='34-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("34"),
        }}
        data-name='34-cell'
        className='cls-18'
        d='M36.04 253.9c0-1.29-1.05-2.34-2.35-2.34H5.3c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34V253.9Z'
      />
      <path
        id='6-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("6"),
        }}
        data-name='6-cell'
        className='cls-8'
        d='M36 220.77c0-1.29-1.05-2.34-2.35-2.34H5.26c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34v-27.62Z'
      />
      <path
        id='27-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("27"),
        }}
        data-name='27-cell'
        className='cls-18'
        d='M35.94 187.64c0-1.29-1.05-2.34-2.35-2.34H5.19c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34z'
      />
      <path
        id='13-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("13"),
        }}
        data-name='13-cell'
        className='cls-8'
        d='M35.97 154.52c0-1.29-1.05-2.34-2.35-2.34H5.23c-1.3 0-2.35 1.05-2.35 2.34v27.62c0 1.29 1.05 2.34 2.35 2.34h28.4c1.3 0 2.35-1.05 2.35-2.34v-27.62Z'
      />
      <path
        id='36-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("36"),
        }}
        data-name='36-cell'
        className='cls-18'
        d='M36.5 121.48c.07-1.32-.99-2.42-2.33-2.42H5.46c-1.31 0-2.37 1.05-2.37 2.34v27.62c0 1.29 1.06 2.34 2.37 2.34h28.61c1.31 0 2.37-1.05 2.37-2.34v-25.87c0-.56.02-1.11.05-1.66Z'
      />
      <path
        id='11-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("11"),
        }}
        data-name='11-cell'
        className='cls-8'
        d='M42.07 90.67c.45-1.2-.13-2.55-1.34-3.03L9.92 75.24c-1.28-.52-2.74.15-3.13 1.45C4.37 84.69 3 93.06 3 101.71v13.58c0 1.29 1.06 2.34 2.38 2.34H34.3c1.26 0 2.3-.97 2.4-2.21.69-8.69 2.49-17 5.35-24.75Z'
      />
      <path
        id='30-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("30"),
        }}
        data-name='30-cell'
        className='cls-18'
        d='M56.74 61.12c.83-.95.79-2.37-.12-3.25L34.28 37.14c-.92-.9-2.42-.9-3.32 0-9.76 9.8-17.39 21.2-22.17 33.73-.44 1.16.15 2.45 1.32 2.92l29.43 11.73c1.21.49 2.59-.08 3.13-1.26 3.59-7.91 8.53-16.8 14.07-23.15Z'
      />
      <path
        id='8-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("8"),
        }}
        data-name='8-cell'
        className='cls-8'
        d='M82.81 40.65c1.14-.56 1.66-1.89 1.17-3.05L73.15 12.48a2.36 2.36 0 0 0-3.05-1.26c-13.01 5.24-24.66 12.5-34.49 21.28-1 .89-1 2.41-.05 3.34l21.6 19.41c.95.92 2.49.9 3.43-.04 6.28-6.19 14.35-10.67 22.21-14.57Z'
      />
      <path
        id='23-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("23"),
        }}
        data-name='23-cell'
        className='cls-18'
        d='M115.9 33.34c1.3-.06 2.34-1.1 2.34-2.37l-.04-27.21c0-1.3-1.08-2.35-2.4-2.31-13.99.38-27.35 2.84-39.68 7.09a2.29 2.29 0 0 0-1.37 3.07l10.34 25.4c.51 1.21 1.94 1.76 3.18 1.27 8.19-3.28 18.45-4.52 27.64-4.93Z'
      />
      <path
        id='10-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("10"),
        }}
        data-name='10-cell'
        className='cls-8'
        d='M149.21 38.81c1.24.48 2.65-.08 3.15-1.28l11.08-25.84c.52-1.23-.11-2.64-1.39-3.07-12.38-4.16-25.78-6.57-39.8-6.84-1.32-.03-2.39 1.03-2.38 2.32l-.02 27.33c0 1.29 1.06 2.33 2.37 2.37 9.23.31 18.75 1.8 26.98 5.02Z'
      />
      <path
        id='5-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("5"),
        }}
        data-name='5-cell'
        className='cls-18'
        d='M177.01 56.72c.95.92 2.48.94 3.42.02l22.55-21.4c.95-.93.93-2.46-.07-3.34-9.95-8.76-21.7-15.99-34.81-21.16-1.2-.47-2.54.1-3.03 1.27l-11.12 26.33c-.49 1.16.04 2.5 1.2 3.05 7.96 3.86 15.51 9.03 21.87 15.23Z'
      />
      <path
        id='24-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("24"),
        }}
        data-name='24-cell'
        className='cls-8'
        d='M194.83 84.58c.54 1.17 1.92 1.74 3.13 1.24l30.88-12.7c1.17-.47 1.76-1.77 1.31-2.93-4.88-12.59-12.66-24.02-22.57-33.82-.91-.9-2.4-.89-3.31 0l-22.64 21.82c-.9.89-.94 2.31-.1 3.26 5.65 6.38 9.64 15.14 13.29 23.12Z'
      />
      <path
        id='16-cell'
        onClick={handleClick}
        style={{
          fill: getPathFill("16"),
        }}
        data-name='16-cell'
        className='cls-18'
        d='M233.69 117.63c1.31 0 2.38-1.05 2.38-2.34v-13.58c0-9.09-1.39-17.38-3.9-25.47-.4-1.3-1.86-1.95-3.14-1.43l-30.82 12.51a2.38 2.38 0 0 0-1.33 3.04c2.94 7.84 4.79 16.26 5.5 25.07.1 1.24 1.14 2.21 2.4 2.21h28.92Z'
      />
      <path
        id='0'
        data-name='0n'
        className='cls-13'
        d='M79.68 566.15c-.73 0-1.35-.18-1.88-.53-.52-.35-.92-.85-1.2-1.52-.28-.66-.42-1.46-.42-2.39s.14-1.72.42-2.37.68-1.15 1.2-1.49 1.15-.52 1.87-.52 1.34.17 1.87.52c.52.34.93.84 1.21 1.49q.42.975.42 2.37c0 .93-.14 1.73-.42 2.39s-.68 1.17-1.2 1.52-1.15.52-1.87.52Zm0-1.49c.5 0 .89-.24 1.19-.73s.44-1.23.44-2.21c0-.64-.07-1.18-.2-1.61s-.32-.75-.56-.97-.53-.32-.86-.32c-.49 0-.89.24-1.18.73-.3.49-.45 1.21-.45 2.18 0 .65.07 1.2.2 1.64.13.43.32.76.57.98s.53.32.86.32h-.02Z'
      />
      <path
        id='10'
        data-name='10n'
        className='cls-13'
        d='M136.25 14.02v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.45 8.69c-.73 0-1.35-.18-1.88-.53-.52-.35-.92-.85-1.2-1.52-.28-.66-.42-1.46-.42-2.39s.14-1.72.42-2.37.68-1.15 1.2-1.49 1.15-.51 1.87-.51 1.34.17 1.87.51.92.84 1.2 1.49.42 1.44.42 2.37-.14 1.73-.42 2.39-.68 1.17-1.2 1.52-1.15.52-1.87.52h.02Zm0-1.49c.5 0 .89-.24 1.19-.73s.44-1.22.44-2.2c0-.64-.07-1.18-.2-1.61s-.32-.75-.56-.97-.53-.32-.86-.32c-.49 0-.89.24-1.18.73-.3.48-.45 1.21-.45 2.18 0 .65.07 1.2.2 1.64.13.43.32.76.57.98s.53.32.86.32l-.02-.02Z'
      />
      <path
        id='8'
        data-name='8n'
        className='cls-13'
        d='M60.33 37.11c-.65 0-1.23-.1-1.73-.31-.51-.21-.9-.5-1.19-.86s-.43-.77-.43-1.23c0-.35.08-.68.25-.98s.39-.55.67-.74c.28-.2.61-.33.95-.38v-.06c-.46-.09-.84-.31-1.12-.66-.29-.35-.43-.76-.43-1.22s.13-.83.39-1.18c.27-.35.62-.62 1.08-.81q.69-.3 1.56-.3c.87 0 1.1.1 1.56.3s.82.47 1.08.82c.27.34.39.73.4 1.17 0 .47-.15.88-.44 1.22q-.435.525-1.11.66v.06c.35.06.66.18.94.38s.51.44.67.74.26.62.26.98c0 .46-.15.87-.43 1.23-.29.36-.68.65-1.19.86s-1.08.31-1.73.31Zm0-1.32c.29 0 .54-.05.76-.15.22-.11.38-.25.51-.43.12-.19.18-.4.18-.64s-.07-.46-.19-.65-.3-.34-.51-.45-.46-.17-.75-.17-.53.06-.75.17-.39.26-.52.45c-.12.19-.18.41-.18.65s.06.45.18.64c.12.18.29.33.51.43s.47.15.76.15m0-3.8c.24 0 .46-.05.65-.15a1.07 1.07 0 0 0 .6-.99c0-.22-.05-.42-.16-.58-.11-.17-.26-.3-.44-.39-.18-.1-.41-.15-.65-.15s-.46.05-.65.15a1.05 1.05 0 0 0-.6.97c0 .22.05.42.16.59s.26.31.44.4c.19.1.41.15.65.15'
      />
      <path
        id='11'
        data-name='11n'
        className='cls-13'
        d='M20.77 95.58v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.82 0v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33z'
      />
      <path
        id='13'
        data-name='13n'
        className='cls-13'
        d='M17.43 164.54v8.5H15.6v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.26 8.62c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.14c.22-.1.38-.23.51-.4.12-.17.18-.37.18-.59s-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42-.23-.1-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37c-.12.16-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.28.38-.68.67-1.18.89s-1.08.32-1.73.32Z'
      />
      <path
        id='6'
        data-name='6n'
        className='cls-13'
        d='M19.91 238.95c-.45 0-.87-.08-1.29-.22s-.78-.38-1.1-.7-.59-.75-.78-1.29c-.19-.53-.28-1.19-.28-1.97 0-.71.08-1.35.25-1.92.17-.56.41-1.04.71-1.44a3.2 3.2 0 0 1 1.11-.9c.43-.21.91-.31 1.45-.31q.87 0 1.53.33c.66.33.8.52 1.06.89.27.38.43.8.49 1.27h-1.8c-.07-.3-.22-.53-.44-.7s-.51-.25-.84-.25q-.84 0-1.29.72c-.29.48-.44 1.14-.44 1.96h.06c.13-.25.31-.47.52-.64.22-.18.47-.32.76-.41.28-.1.58-.14.9-.14.51 0 .97.12 1.37.36s.71.56.95.97c.23.41.35.89.35 1.42 0 .58-.13 1.09-.41 1.54s-.65.8-1.14 1.05c-.48.25-1.05.38-1.69.37Zm-.01-1.41c.28 0 .53-.07.76-.2.22-.13.4-.31.52-.54s.19-.48.19-.76-.07-.54-.19-.76-.3-.4-.51-.54c-.22-.13-.47-.2-.76-.2-.21 0-.41.04-.59.12-.17.08-.33.19-.46.32-.13.14-.23.3-.32.48-.07.18-.12.37-.12.58 0 .27.07.52.19.75.13.23.31.41.52.54.22.14.47.2.76.2Z'
      />
      <path
        id='17'
        data-name='17n'
        className='cls-13'
        d='M17.79 296.58v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm2.4 8.5 3.59-6.98v-.06H19.6v-1.47h6.07v1.49l-3.59 7.01h-1.89Z'
      />
      <path
        id='2'
        data-name='2n'
        className='cls-13'
        d='M16.75 371.39v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.4.41-.59.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45s-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Z'
      />
      <path
        id='4'
        data-name='4n'
        className='cls-13'
        d='M16.31 435.67v-1.41l3.61-5.59h1.25v1.96h-.74l-2.27 3.54v.07h5.13v1.44h-6.97Zm4.15 1.5v-1.93l.03-.63v-5.95h1.72v8.5h-1.75Z'
      />
      <path
        id='15'
        data-name='15n'
        className='cls-13'
        d='M21.66 497.21v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.21 8.62c-.6 0-1.13-.11-1.59-.32-.46-.22-.83-.51-1.1-.89s-.42-.81-.42-1.3h1.78c.02.33.16.6.42.8.27.2.57.31.93.31.29 0 .54-.06.76-.19s.4-.3.52-.53.19-.49.19-.78-.07-.56-.19-.79-.3-.4-.53-.53a1.6 1.6 0 0 0-.77-.2c-.26 0-.5.05-.74.15-.23.1-.42.24-.56.42l-1.63-.29.41-4.48h5.29v1.47h-3.78l-.22 2.13h.05c.15-.21.38-.38.69-.52s.65-.21 1.03-.21c.52 0 .99.12 1.39.36s.73.57.97.99c.23.42.36.9.35 1.45 0 .57-.13 1.08-.41 1.53q-.405.66-1.14 1.05c-.48.25-1.05.38-1.69.38Z'
      />
      <path
        id='26'
        data-name='26n'
        className='cls-13'
        d='M115.58 573.1v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.59c.09-.19.14-.4.14-.63 0-.25-.06-.46-.17-.64s-.27-.32-.47-.42-.42-.15-.68-.15-.5.05-.69.16-.35.26-.46.45c-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95c.46-.22 1-.34 1.59-.34s1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm10.81.11c-.45 0-.87-.08-1.29-.22s-.78-.38-1.1-.7-.59-.75-.78-1.29c-.19-.53-.28-1.19-.28-1.97 0-.71.08-1.35.25-1.91s.4-1.04.71-1.44a3.2 3.2 0 0 1 1.11-.9c.43-.21.91-.31 1.45-.31q.87 0 1.53.33c.44.22.8.52 1.06.89.27.38.43.8.49 1.27h-1.8c-.07-.3-.22-.53-.44-.7s-.51-.25-.84-.25q-.84 0-1.29.72c-.29.48-.44 1.14-.44 1.96h.06c.13-.25.31-.47.52-.64.22-.18.47-.32.76-.41q.42-.15.9-.15c.51 0 .97.12 1.37.36s.71.56.95.97.35.89.35 1.42c0 .58-.13 1.09-.41 1.54q-.405.675-1.14 1.05c-.48.25-1.05.38-1.69.37Zm-.01-1.41c.28 0 .53-.07.76-.2s.4-.31.52-.54a1.62 1.62 0 0 0 0-1.52c-.12-.22-.3-.4-.51-.54-.22-.13-.47-.2-.76-.2-.22 0-.41.04-.59.12-.17.08-.33.18-.46.32s-.24.3-.32.48c-.07.18-.12.37-.12.58 0 .27.07.52.19.75.13.23.31.41.52.54.22.14.47.2.76.2Z'
      />
      <path
        id='35'
        data-name='35n'
        className='cls-13'
        d='M192.32 541.09c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.225.51.36c.22.09.46.13.72.13s.52-.05.74-.15.38-.23.51-.4c.12-.17.18-.37.18-.59s-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42s-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.37.17-.59-.05-.4-.16-.56c-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37c-.12.16-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .735-.42 1.29c-.28.38-.68.67-1.18.89s-1.08.32-1.73.32v.02Zm7.8 0c-.6 0-1.13-.11-1.59-.32-.46-.22-.83-.51-1.1-.89s-.42-.81-.42-1.3h1.78c.02.33.16.6.42.8.27.21.57.31.93.31.29 0 .54-.06.76-.19s.4-.3.52-.53.19-.49.19-.78-.07-.56-.19-.79-.31-.4-.53-.53-.48-.19-.77-.19c-.26 0-.5.05-.74.15-.23.1-.42.24-.56.42l-1.63-.29.41-4.49h5.29v1.47h-3.78l-.22 2.13h.05c.15-.21.38-.38.69-.52s.65-.21 1.03-.21c.52 0 .99.12 1.39.36s.73.57.96.99c.24.42.36.9.35 1.45 0 .57-.13 1.08-.41 1.53q-.405.66-1.14 1.05c-.48.25-1.05.38-1.69.38Z'
      />
      <path
        id='28'
        data-name='28n'
        className='cls-13'
        d='M212.69 470.71v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.58.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm10.72.11c-.65 0-1.23-.1-1.73-.31s-.9-.5-1.19-.86-.43-.77-.43-1.23c0-.35.08-.68.25-.98s.39-.55.68-.74c.28-.2.61-.33.95-.38v-.06c-.46-.09-.84-.31-1.12-.66-.29-.35-.43-.76-.43-1.23 0-.44.13-.83.39-1.18.27-.35.62-.62 1.08-.81q.69-.3 1.56-.3c.87 0 1.1.1 1.56.3s.82.47 1.08.82c.27.34.4.73.4 1.17 0 .47-.15.88-.44 1.23s-.66.57-1.11.66v.06a2.3 2.3 0 0 1 1.62 1.12c.17.3.26.62.26.98 0 .46-.15.87-.43 1.23-.29.36-.68.65-1.19.86s-1.08.31-1.73.31h-.02Zm0-1.32c.29 0 .54-.05.76-.15.22-.11.39-.25.51-.43s.18-.4.18-.64-.07-.46-.19-.65-.3-.34-.51-.45c-.22-.11-.46-.17-.75-.17s-.53.06-.75.17q-.33.165-.51.45c-.18.285-.18.41-.18.65s.06.45.18.64c.12.18.29.33.51.43s.47.15.76.15h-.02Zm0-3.79c.24 0 .46-.05.65-.15a1.07 1.07 0 0 0 .6-.99c0-.22-.05-.42-.16-.58-.11-.17-.26-.3-.44-.39-.18-.1-.41-.15-.65-.15s-.46.05-.65.15c-.19.09-.34.22-.44.39s-.16.36-.16.58.05.42.16.59.26.31.44.4c.19.1.41.15.65.15'
      />
      <path
        id='29'
        data-name='29n'
        className='cls-13'
        d='M212.69 404.46v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.58.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.7.16-.35.26-.46.45c-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.17Zm10.63-8.62c.45 0 .87.07 1.29.22s.78.38 1.1.7.58.75.77 1.28.29 1.18.29 1.95c0 .72-.08 1.37-.25 1.94-.17.56-.4 1.05-.71 1.44q-.465.6-1.11.9c-.43.2-.92.31-1.45.31-.57 0-1.09-.11-1.53-.33s-.8-.52-1.07-.9q-.405-.57-.48-1.29h1.8c.07.31.22.55.44.72s.51.25.84.25q.84 0 1.29-.72c.29-.49.44-1.15.44-1.99h-.06c-.13.25-.31.46-.52.64-.22.18-.47.31-.76.41s-.58.14-.9.14c-.51 0-.96-.12-1.36-.35-.4-.24-.71-.56-.95-.97s-.35-.88-.35-1.41c0-.57.13-1.08.4-1.52.27-.45.65-.8 1.13-1.05.49-.25 1.05-.38 1.7-.38Zm0 1.41c-.28 0-.54.07-.76.2s-.39.31-.52.54c-.12.22-.19.47-.18.75 0 .28.07.53.18.76.12.22.3.4.51.53.22.13.47.2.76.2.22 0 .41-.04.59-.12s.34-.18.46-.32c.13-.14.24-.3.32-.48.07-.18.12-.37.12-.57a1.49 1.49 0 0 0-.71-1.28c-.22-.14-.47-.2-.76-.2Z'
      />
      <path
        id='22'
        data-name='22n'
        className='cls-13'
        d='M212.86 338.2v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.58.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16-.35.26-.46.45c-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm7.48 0v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.58.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Z'
      />
      <path
        id='31'
        data-name='31n'
        className='cls-13'
        d='M217.12 272.07c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.225.51.36c.315.135.46.13.72.13s.52-.05.74-.15.38-.23.5-.4.18-.37.18-.59-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42s-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.49.37-.19.35-.2.56H214c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.42.57-.68.67-1.18.89s-1.08.32-1.73.32v.02Zm8.41-8.62v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33z'
      />
      <path
        id='20'
        data-name='20n'
        className='cls-13'
        d='M213.66 204.3V203l3.08-2.8c.27-.25.48-.47.66-.67s.32-.4.41-.59.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm10.88.18c-.73 0-1.35-.18-1.88-.53s-.92-.85-1.2-1.52c-.28-.66-.42-1.46-.42-2.39s.14-1.72.42-2.37.68-1.15 1.2-1.49 1.15-.51 1.87-.51 1.34.17 1.87.51.93.84 1.2 1.49q.42.975.42 2.37c0 .93-.14 1.73-.42 2.39s-.68 1.17-1.2 1.52-1.15.52-1.87.52h.02Zm0-1.49c.5 0 .89-.25 1.19-.74s.44-1.22.44-2.2c0-.65-.07-1.18-.2-1.61s-.32-.75-.56-.97-.53-.32-.86-.32c-.49 0-.89.24-1.18.73-.3.48-.45 1.21-.45 2.18 0 .65.07 1.2.2 1.64.13.43.32.76.57.98s.53.32.86.32h-.02Z'
      />
      <path
        id='33'
        data-name='33n'
        className='cls-13'
        d='M215.74 139.57c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.14c.22-.1.38-.23.5-.4s.18-.37.18-.59-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42s-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.49.37-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.28.38-.68.67-1.18.89s-1.08.32-1.73.32Zm7.84 0c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.14c.22-.1.38-.23.5-.4s.18-.37.18-.59-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42s-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .51-.05.73-.14s.38-.22.5-.39.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.49.37-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.28.38-.68.67-1.18.89s-1.08.32-1.72.32h-.02Z'
      />
      <path
        id='24'
        data-name='24n'
        className='cls-13'
        d='M197.72 65.33v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.59c.09-.19.14-.4.14-.63 0-.25-.06-.46-.17-.64s-.27-.32-.47-.42-.42-.15-.68-.15-.5.05-.7.16-.35.26-.46.45c-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.17Zm7.18-1.5v-1.42l3.61-5.59h1.25v1.96h-.74l-2.27 3.54v.07h5.13v1.44h-6.97Zm4.15 1.5V63.4l.03-.63v-5.95h1.72v8.5h-1.75Z'
      />
      <path
        id='23'
        data-name='23n'
        className='cls-13'
        d='M92 21.89v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.59c.09-.19.14-.4.14-.63 0-.25-.06-.46-.17-.64s-.27-.32-.47-.42-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95c.46-.22 1-.34 1.59-.34s1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm10.69.12c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55.13.15.3.28.51.36.22.09.46.13.72.13s.52-.05.74-.15.38-.23.51-.4.18-.37.18-.59-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42-.23-.1-.51-.15-.85-.15h-.81V16.9h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.37.17-.59-.05-.4-.16-.56c-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.28.38-.68.67-1.18.89s-1.08.32-1.73.32v.02Z'
      />
      <path
        id='30'
        data-name='30n'
        className='cls-13'
        d='M29.58 66.44c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.15.38-.23.51-.4c.12-.17.18-.37.18-.59s-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42-.23-.1-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.37.17-.59-.05-.4-.16-.56c-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37c-.12.16-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.42.57-.68.67-1.18.89s-1.08.32-1.73.32v.02Zm8.03.07c-.73 0-1.35-.18-1.88-.53-.52-.35-.92-.85-1.2-1.52-.28-.66-.42-1.46-.42-2.39s.14-1.72.42-2.37.68-1.15 1.2-1.49 1.15-.51 1.87-.51 1.34.17 1.87.51.93.84 1.21 1.49.42 1.44.42 2.37-.14 1.73-.42 2.39-.68 1.17-1.2 1.52-1.15.52-1.87.52Zm0-1.49c.5 0 .89-.24 1.19-.73s.44-1.22.44-2.2c0-.64-.07-1.18-.2-1.61s-.32-.75-.56-.97-.53-.32-.86-.32c-.49 0-.89.24-1.18.73-.3.48-.45 1.21-.45 2.18 0 .65.07 1.2.2 1.64.13.43.32.76.57.98s.53.32.86.32l-.02-.02Z'
      />
      <path
        id='36'
        data-name='36n'
        className='cls-13'
        d='M15.84 139.57c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.14c.22-.1.38-.23.51-.4.12-.17.18-.37.18-.59s-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42-.23-.1-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37c-.12.16-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.28.38-.68.67-1.18.89s-1.08.32-1.73.32Zm7.96 0c-.45 0-.87-.08-1.29-.22s-.78-.38-1.1-.7-.59-.75-.78-1.29c-.19-.53-.28-1.19-.28-1.97 0-.71.08-1.35.25-1.91s.41-1.04.71-1.44a3.2 3.2 0 0 1 1.11-.9c.43-.21.91-.31 1.45-.31q.87 0 1.53.33c.66.33.8.52 1.06.89.27.38.43.8.49 1.27h-1.8c-.07-.3-.22-.53-.44-.7s-.51-.25-.84-.25q-.84 0-1.29.72c-.29.48-.44 1.14-.44 1.96h.06c.13-.25.31-.47.52-.64.22-.18.47-.32.76-.41q.42-.15.9-.15c.51 0 .97.12 1.37.36s.71.56.95.97c.23.41.35.88.35 1.42 0 .58-.13 1.09-.41 1.54q-.405.675-1.14 1.05c-.48.25-1.05.38-1.69.37Zm-.01-1.41c.28 0 .53-.07.76-.2.22-.13.4-.31.52-.54s.19-.48.19-.76-.07-.54-.19-.76-.3-.4-.51-.54c-.22-.13-.47-.2-.76-.2-.21 0-.41.04-.59.12-.17.08-.33.19-.46.32-.13.14-.23.3-.32.48-.07.18-.12.37-.12.58 0 .27.07.52.19.75.13.23.31.41.52.54.22.14.47.2.76.2Z'
      />
      <path
        id='27'
        data-name='27n'
        className='cls-13'
        d='M12.78 205.73v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.59c.09-.19.14-.4.14-.63 0-.25-.06-.46-.17-.64s-.27-.32-.47-.42-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45s-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm7.83 0 3.59-6.98v-.06h-4.18v-1.47h6.07v1.49l-3.59 7.01h-1.88Z'
      />
      <path
        id='34'
        data-name='34n'
        className='cls-13'
        d='M15.69 272.07c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.225.51.36c.315.135.46.13.72.13s.52-.05.74-.15.38-.23.51-.4.18-.37.18-.59-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42-.23-.1-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.42.57-.68.67-1.18.89s-1.08.32-1.73.32v.02Zm4.52-1.61v-1.41l3.61-5.59h1.25v1.96h-.74l-2.27 3.54v.07h5.13v1.44h-6.97Zm4.15 1.5v-1.93l.03-.63v-5.95h1.72v8.5h-1.75Z'
      />
      <path
        id='25'
        data-name='25n'
        className='cls-13'
        d='M12.9 338.21v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.4.41-.59.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16-.35.26-.46.45c-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm10.65.11c-.6 0-1.13-.11-1.59-.32-.46-.22-.83-.51-1.1-.89s-.42-.81-.42-1.3h1.78c.02.33.16.6.42.8.27.2.57.31.93.31.29 0 .54-.06.76-.19s.4-.3.52-.53.19-.49.19-.78-.07-.56-.19-.79-.3-.4-.53-.53a1.6 1.6 0 0 0-.77-.2c-.26 0-.5.05-.74.15-.23.1-.42.24-.56.42l-1.63-.29.41-4.49h5.29v1.47h-3.78l-.22 2.13h.05c.15-.21.38-.38.69-.52s.65-.21 1.03-.21c.52 0 .99.12 1.39.36s.73.57.97.99c.23.42.36.9.35 1.45 0 .57-.13 1.08-.41 1.53q-.405.66-1.14 1.05c-.48.25-1.05.38-1.69.38Z'
      />
      <path
        id='21'
        data-name='21n'
        className='cls-13'
        d='M14.21 404.52v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.4.41-.59.14-.4.14-.63c0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Zm11.26-8.51v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33z'
      />
      <path
        id='19'
        data-name='19n'
        className='cls-13'
        d='M17.41 462.21v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.18-.12c.45 0 .87.07 1.29.22s.78.38 1.1.7.58.75.77 1.28.29 1.18.29 1.95c0 .72-.08 1.37-.25 1.94-.17.56-.4 1.05-.71 1.44-.31.4-.68.7-1.11.91-.43.2-.92.31-1.45.31q-.87 0-1.53-.33c-.66-.33-.8-.52-1.07-.9s-.43-.81-.49-1.29h1.8c.07.31.22.55.44.72s.51.25.84.25q.84 0 1.29-.72c.29-.48.44-1.15.44-1.99h-.06c-.13.25-.31.46-.52.64-.22.18-.47.31-.76.41s-.58.14-.9.14c-.51 0-.96-.12-1.36-.35-.4-.24-.71-.56-.95-.97-.23-.41-.35-.88-.35-1.41 0-.57.13-1.08.4-1.52.27-.45.65-.8 1.13-1.05.49-.25 1.05-.38 1.7-.38Zm.01 1.41c-.28 0-.54.07-.76.2s-.39.31-.52.54c-.12.22-.19.47-.18.75 0 .28.06.53.18.76.12.22.3.4.51.53s.47.2.76.2c.21 0 .41-.04.59-.12s.34-.18.46-.32c.13-.14.24-.3.32-.48s.12-.37.12-.57c0-.27-.07-.51-.19-.74-.12-.22-.3-.4-.52-.54s-.47-.2-.76-.2Z'
      />
      <path
        id='32'
        data-name='32n'
        className='cls-13'
        d='M42.47 541.47c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.14c.22-.1.38-.23.51-.4.12-.17.18-.37.18-.59s-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42-.23-.1-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59-.05-.4-.16-.56c-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.5.37c-.12.16-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.42.57-.68.67-1.18.89s-1.08.32-1.73.32Zm4.64-.12v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.58.14-.4.14-.63c0-.25-.06-.46-.17-.64s-.27-.32-.47-.42-.42-.15-.68-.15-.5.05-.69.16c-.2.11-.35.26-.46.45-.11.2-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95c.46-.22 1-.34 1.59-.34s1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.18Z'
      />
      <path
        id='3'
        data-name='3n'
        className='cls-13'
        d='M163.79 563.48c-.63 0-1.2-.11-1.69-.32-.49-.22-.88-.51-1.16-.89s-.42-.82-.43-1.31h1.84c0 .21.08.39.21.55q.195.24.51.36c.22.09.46.13.72.13s.52-.05.74-.14c.22-.1.38-.23.5-.4s.18-.37.18-.59-.07-.43-.19-.6c-.12-.18-.31-.32-.55-.42s-.51-.15-.85-.15h-.81v-1.32h.81c.27 0 .52-.05.73-.14.22-.09.38-.22.5-.39s.17-.36.17-.59c0-.21-.05-.4-.16-.56-.1-.16-.25-.29-.43-.38s-.4-.14-.64-.14-.47.04-.68.13-.37.21-.49.37-.19.35-.2.56h-1.75c0-.49.15-.92.42-1.3.27-.37.65-.67 1.11-.88.47-.21 1-.32 1.59-.32s1.12.11 1.57.32.8.5 1.05.86.37.76.37 1.21c0 .48-.15.87-.45 1.19s-.7.52-1.18.61v.07c.64.08 1.12.3 1.45.65s.5.79.5 1.32q0 .72-.42 1.29c-.42.57-.68.67-1.18.89s-1.08.32-1.73.32Z'
      />
      <path
        id='12'
        data-name='12n'
        className='cls-13'
        d='M214.52 497.33v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm2.06 8.5v-1.3l3.08-2.8c.27-.25.48-.47.66-.67s.32-.39.41-.59c.09-.19.14-.4.14-.63 0-.25-.06-.46-.17-.64-.12-.18-.27-.32-.47-.42s-.42-.15-.68-.15-.5.05-.7.16-.35.26-.46.45-.16.43-.16.7h-1.73c0-.56.12-1.04.38-1.45s.61-.73 1.08-.95 1-.34 1.59-.34 1.16.11 1.62.32.82.51 1.08.89.38.81.38 1.3c0 .32-.07.64-.19.95s-.36.66-.68 1.04-.79.83-1.38 1.37l-1.26 1.22v.06h3.64v1.47h-6.17Z'
      />
      <path
        id='7'
        data-name='7n'
        className='cls-13'
        d='m217.25 437.58 3.59-6.98v-.06h-4.18v-1.47h6.07v1.49l-3.59 7.01h-1.89Z'
      />
      <path
        id='18'
        data-name='18n'
        className='cls-13'
        d='M217.08 362.6v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.29 8.62c-.65 0-1.23-.1-1.73-.31s-.9-.5-1.19-.86-.43-.77-.43-1.23c0-.35.08-.68.25-.98s.39-.55.67-.74c.28-.2.61-.33.95-.38v-.06c-.46-.09-.84-.31-1.12-.66-.29-.35-.43-.76-.43-1.22s.13-.83.39-1.18c.27-.35.62-.62 1.08-.81q.69-.3 1.56-.3c.87 0 1.1.1 1.56.3s.82.47 1.08.82c.27.34.39.73.4 1.17 0 .47-.15.88-.44 1.22q-.435.525-1.11.66v.06c.35.05.66.18.94.38s.51.44.67.74.26.62.26.98c0 .46-.15.87-.43 1.23-.29.36-.69.65-1.2.86s-1.08.31-1.73.31m0-1.32c.29 0 .54-.05.76-.15.22-.11.39-.25.51-.43.12-.19.18-.4.18-.64s-.07-.46-.19-.65-.3-.34-.51-.45c-.22-.11-.46-.17-.75-.17s-.53.05-.75.17c-.22.11-.39.26-.52.45-.12.19-.18.41-.18.65s.06.45.18.64c.12.18.29.33.51.43s.47.15.76.15m0-3.8c.24 0 .46-.05.65-.15s.33-.23.44-.4.16-.37.16-.59-.05-.42-.16-.58c-.11-.17-.26-.3-.44-.39-.18-.1-.4-.14-.65-.14s-.46.05-.65.14a1.05 1.05 0 0 0-.6.97c0 .22.05.42.16.59s.26.31.44.4c.19.1.41.15.65.15'
      />
      <path
        id='9'
        data-name='9n'
        className='cls-13'
        d='M219.53 296.37c.45 0 .87.07 1.29.22s.78.38 1.1.7.58.75.77 1.28.29 1.18.29 1.95c0 .72-.08 1.37-.25 1.94-.17.56-.4 1.05-.71 1.44-.31.4-.68.7-1.11.91-.43.2-.91.31-1.45.31-.57 0-1.09-.11-1.53-.33s-.8-.52-1.07-.9q-.405-.57-.48-1.29h1.8c.07.31.22.55.44.72s.51.25.84.25q.84 0 1.29-.72c.29-.48.44-1.15.44-1.99h-.06c-.13.25-.31.46-.52.64-.22.18-.47.31-.76.41s-.58.14-.9.14c-.51 0-.96-.12-1.36-.35-.4-.24-.71-.56-.95-.97-.23-.41-.35-.88-.35-1.41 0-.57.13-1.08.4-1.52.27-.45.65-.8 1.13-1.05.49-.25 1.05-.38 1.69-.38Zm.01 1.41c-.28 0-.54.07-.76.2s-.4.31-.52.54c-.12.22-.19.47-.18.75 0 .28.06.53.18.76.12.22.3.4.51.53s.47.2.76.2c.21 0 .41-.04.59-.12s.34-.18.46-.32c.13-.14.24-.3.32-.48.07-.18.12-.37.12-.57 0-.27-.07-.51-.19-.74-.12-.22-.3-.4-.52-.54s-.47-.2-.76-.2Z'
      />
      <path
        id='14'
        data-name='14n'
        className='cls-13'
        d='M217.17 230.33v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm1.92 7.01v-1.41l3.61-5.59h1.25v1.96h-.74l-2.27 3.54v.07h5.13v1.44h-6.97Zm4.16 1.49v-1.93l.03-.63v-5.95H225v8.5h-1.75Z'
      />
      <path
        id='1'
        data-name='1n'
        className='cls-13'
        d='M221.61 164.08v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33z'
      />
      <path
        id='16'
        data-name='16n'
        className='cls-13'
        d='M217.14 92.91v8.5h-1.83v-6.8h-.05l-1.98 1.22v-1.59l2.14-1.33zm5.37 8.62c-.45 0-.87-.08-1.29-.22s-.78-.38-1.1-.7q-.48-.48-.78-1.29c-.19-.53-.28-1.19-.28-1.97 0-.71.08-1.35.25-1.91s.41-1.04.71-1.44q.465-.585 1.11-.9c.645-.315.91-.31 1.45-.31.57 0 1.09.11 1.53.33s.8.52 1.06.89c.27.38.43.8.49 1.27h-1.8c-.07-.3-.22-.53-.44-.7s-.51-.25-.84-.25q-.84 0-1.29.72c-.29.48-.44 1.14-.44 1.96h.06c.13-.25.31-.47.52-.64.22-.18.47-.32.76-.41q.42-.15.9-.15c.51 0 .97.12 1.37.36s.71.56.95.97c.23.41.35.88.35 1.42 0 .58-.13 1.09-.41 1.54q-.405.675-1.14 1.05c-.48.25-1.05.38-1.69.37h-.02Zm-.01-1.41c.28 0 .53-.07.76-.2s.4-.31.52-.54a1.62 1.62 0 0 0 0-1.52c-.12-.22-.3-.4-.51-.54-.22-.13-.47-.2-.76-.2-.21 0-.41.04-.59.12-.17.08-.33.19-.46.32-.13.14-.24.3-.32.48s-.12.37-.12.58c0 .27.07.52.19.75.13.23.31.41.52.54.22.14.47.2.76.2Z'
      />
      <path
        id='5'
        data-name='5n'
        className='cls-13'
        d='M175.42 35.19c-.6 0-1.13-.11-1.59-.32-.46-.22-.83-.51-1.1-.89s-.42-.81-.42-1.3h1.78c.02.33.16.6.42.8.27.2.57.31.93.31.29 0 .54-.06.76-.19s.4-.3.51-.53c.12-.23.19-.49.19-.78s-.07-.56-.19-.79-.31-.4-.53-.53a1.6 1.6 0 0 0-.77-.2c-.26 0-.5.05-.74.15s-.42.24-.56.42l-1.63-.29.41-4.48h5.29v1.47h-3.78l-.22 2.13h.05c.15-.21.38-.38.69-.52s.65-.21 1.03-.21c.52 0 .99.12 1.39.36q.615.36.96.99c.24.42.36.9.35 1.44 0 .57-.13 1.08-.41 1.53q-.405.66-1.14 1.05c-.48.25-1.05.38-1.69.38Z'
      />
      <rect
        id='small-series-button'
        onClick={() => handleSpecialButtonClick(Tiers)}
        className='active:brightness-75 duration-100 ease-in-out cursor-pointer'
        x={68.11}
        y={102.96}
        width={101.1}
        height={29.23}
        rx={7}
        ry={7}
        style={{
          stroke: "#c30707",
          strokeWidth: 2,
        }}
      />
      <rect
        id='big-series-button'
        onClick={() => handleSpecialButtonClick(Voisins)}
        className='active:brightness-75 duration-100 ease-in-out cursor-pointer'
        x={68.11}
        y={385.48}
        width={101.1}
        height={29.23}
        rx={7}
        ry={7}
        style={{
          strokeWidth: 2,
          stroke: "#eed600",
        }}
      />
      <rect
        id='orphalins-button'
        onClick={() => handleSpecialButtonClick(Orphalins)}
        className='active:brightness-75 duration-100 ease-in-out cursor-pointer'
        x={68.11}
        y={241.08}
        width={101.1}
        height={29.23}
        rx={7}
        ry={7}
        style={{
          strokeWidth: 2,
          stroke: "#1c83e4",
        }}
      />
      <rect
        id='zero-spiel-button'
        onClick={() => handleSpecialButtonClick(Zeros)}
        className='active:brightness-75 duration-100 ease-in-out cursor-pointer'
        x={68.11}
        y={492.39}
        width={101.1}
        height={29.23}
        rx={7}
        ry={7}
        style={{
          strokeWidth: 2,
          stroke: "#0cae12",
        }}
      />
      <path
        id='ss-outline'
        d='m40.36 217.16.42-99.53s-1.6-80.17 77.46-80.12c80.99.05 79.6 82.28 79.6 82.28l.12 31.98z'
        style={{
          fill: "none",
          strokeMiterlimit: 10,
          strokeWidth: 3,
          stroke: "#ef3f3f",
        }}
      />
      <path
        id='orph-outline'
        style={{
          fill: "none",
          strokeMiterlimit: 10,
          strokeWidth: 3,
          stroke: "#60bcea",
        }}
        d='M40.3 220.13v95.15l159.01.03-1.25-160.49z'
      />
      <path
        id='bs-outline'
        style={{
          fill: "none",
          strokeMiterlimit: 10,
          strokeWidth: 3,
          stroke: "#f2ec59",
        }}
        d='m40.27 318.31-.84 163.65 158.54-.09 1.34-163.63z'
      />
      <path
        id='zeros-outline'
        d='M39.76 484.96s12.74 62.37 80.16 63.06c67.41.69 77.78-63.11 77.78-63.11l-157.93.06Z'
        style={{
          stroke: "#83c561",
          fill: "none",
          strokeMiterlimit: 10,
          strokeWidth: 3,
        }}
      />
      <text
        transform='matrix(.63 0 0 1 78.97 124.28)'
        className='cursor-default pointer-events-none select-none'
        style={{
          fill: "#cd242b",
          fontFamily: "MicrosoftSansSerif,&quot",
          fontSize: 18,
        }}
      >
        <tspan x={0} y={0}>
          {"SMALL SERIES"}
        </tspan>
      </text>
      <text
        transform='matrix(.63 0 0 1 85.64 262.21)'
        className='cursor-default pointer-events-none select-none'
        style={{
          fontFamily: "MicrosoftSansSerif,&quot",
          fontSize: 18,
          fill: "#1c83e4",
        }}
      >
        <tspan x={0} y={0}>
          {"ORPHALINS"}
        </tspan>
      </text>
      <text
        transform='matrix(.63 0 0 1 85.66 406.69)'
        className='cursor-default pointer-events-none select-none'
        style={{
          fontFamily: "MicrosoftSansSerif,&quot",
          fontSize: 18,
          fill: "#eed600",
        }}
      >
        <tspan x={0} y={0}>
          {"BIG SERIES"}
        </tspan>
      </text>
      <text
        transform='matrix(.63 0 0 1 84.11 513)'
        className='cursor-default pointer-events-none select-none'
        style={{
          fontFamily: "MicrosoftSansSerif,&quot",
          fontSize: 18,
        }}
      >
        <tspan x={0} y={0}>
          {"ZERO SPIEL"}
        </tspan>
      </text>
      <text
        transform='matrix(.63 0 0 1 84.11 513)'
        className='cursor-default pointer-events-none select-none'
        style={{
          fontFamily: "MicrosoftSansSerif,&quot",
          fontSize: 18,
          fill: "#0cae12",
        }}
      >
        <tspan x={0} y={0}>
          {"ZERO SPIEL"}
        </tspan>
      </text>
    </svg>
  )
}

// Main component with error boundary
const MobileSpecialsTable: React.FC = () => {
  return (
    <EnhancedErrorBoundary context='MobileSpecialsTable'>
      <MobileSpecialsTableInner />
    </EnhancedErrorBoundary>
  )
}

export default MobileSpecialsTable
