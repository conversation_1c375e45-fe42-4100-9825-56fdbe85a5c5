import GameSection from "@/components/ui/game-section"
import { BonusBetsPanel } from "@/features/bonus-bets"
import PastResultsPanel from "@/features/past-results/past-results-panel"
import { NeighboursButtons } from "@/features/roulette-boards/components/neighbours"
import RouletteTable from "@/features/roulette-boards/roulette-table"
import SideMenu from "@/features/side-menu/side-menu"
import StatisticsPanel from "@/features/statistics/statistics-panel"
import VideoPlayer from "@/features/streaming/video-player"
import WinnersPanel from "@/features/winners/winners-panel"
import { cn } from "@/lib/utils"
import { GAME_SECTION_STYLES } from "./layout-constants"

interface DesktopBettingAreaProps {
  gameType: string
}

const DesktopTopInformationRow = () => (
  <>
    <GameSection
      hasInsetShadow={false}
      className='w-full h-full col-start-1 row-start-1'
      title='History'
    >
      <section className='flex h-full flex-col gap-2'>
        <SideMenu />
        <WinnersPanel />
      </section>
    </GameSection>

    <GameSection
      style={{ boxShadow: GAME_SECTION_STYLES.STANDARD_SHADOW }}
      className={cn(
        "col-start-2 row-start-1",
        GAME_SECTION_STYLES.BACKGROUND_OVERLAY
      )}
      title='Statistics'
    >
      <StatisticsPanel />
    </GameSection>

    <GameSection
      style={{ boxShadow: GAME_SECTION_STYLES.VIDEO_SHADOW }}
      className='w-full h-full overflow-hidden col-start-3 row-start-1 relative'
      title='Video'
    >
      <VideoPlayer />
    </GameSection>

    <GameSection
      style={{ boxShadow: GAME_SECTION_STYLES.STANDARD_SHADOW }}
      className={cn(
        "col-start-4 row-start-1",
        GAME_SECTION_STYLES.BACKGROUND_OVERLAY
      )}
      title='Past Results'
    >
      <PastResultsPanel />
    </GameSection>
  </>
)

const DesktopBettingArea = ({ gameType }: DesktopBettingAreaProps) => (
  <>
    <GameSection
      className={cn(
        GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
        GAME_SECTION_STYLES.BONUS_BORDER,
        "col-start-1 row-start-2 flex-grow min-h-0 flex flex-col"
      )}
      title='Bonus Bets'
    >
      <div className='flex-1 min-h-0 overflow-hidden'>
        <BonusBetsPanel />
      </div>
    </GameSection>

    <GameSection
      hasInsetShadow={false}
      className={cn(
        "col-start-2 row-start-2 col-span-full flex-grow overflow-hidden min-h-0 flex flex-col",
        gameType === "special"
          ? "col-span-3"
          : GAME_SECTION_STYLES.BACKGROUND_OVERLAY
      )}
      title='Betting Table'
    >
      <div className='flex flex-1 gap-1 flex-row min-h-0 overflow-hidden'>
        <RouletteTable />
        <NeighboursButtons />
      </div>
    </GameSection>
  </>
)

export { DesktopTopInformationRow, DesktopBettingArea }
